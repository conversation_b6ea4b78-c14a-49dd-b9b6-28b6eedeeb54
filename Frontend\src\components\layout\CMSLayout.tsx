
import React, { useState, useEffect } from 'react';
import { Link, useLocation, useNavigate } from 'react-router-dom';
import {
  Boxes,
  Database,
  Layers,
  LayoutDashboard,
  Settings,
  ChevronDown,
  ChevronRight,
  Building,
  Folder,
  FolderOpen,
  Users,
  Tag
} from 'lucide-react';
import { useAuthStore, useClickedClient } from '@/lib/store';
import { toast } from 'sonner';
import { authApi, clientsApi, parentCategoriesApi, categoriesApi } from '@/lib/api';
import { Header } from './Header';

import {
  Sidebar,
  SidebarContent,
  SidebarHeader,
  SidebarMenu,
  SidebarMenuButton,
  SidebarMenuItem,
  SidebarMenuSub,
  SidebarMenuSubItem,
  SidebarMenuSubButton,
  SidebarFooter,
  SidebarTrigger,
  SidebarProvider
} from '@/components/ui/sidebar';

interface CMSLayoutProps {
  children: React.ReactNode;
}

interface Client {
  id: number;
  name: string;
  createdBy: string;
  createdAt: string;
  modifiedBy: string;
  modifiedAt: string;
}

interface ParentCategory {
  id: number;
  categoryName: string;
  client: {
    id: number;
    name: string;
  };
  createdBy: string;
  createdAt: string;
  modifiedBy: string;
  modifiedAt: string;
}

interface Category {
  id: number;
  categoryName: string;
  parentCategory: {
    id: number;
    categoryName: string;
  };
  client: {
    id: number;
    name: string;
  };
  createdBy: string;
  createdAt: string;
  modifiedBy: string;
  modifiedAt: string;
}

interface ClientHierarchy {
  client: Client;
  parentCategories: {
    parentCategory: ParentCategory;
    childCategories: Category[];
  }[];
}

export function CMSLayout({ children }: CMSLayoutProps) {
  const { isAuthenticated, user, token, logout } = useAuthStore();
  const navigate = useNavigate();
  const location = useLocation();
  const [isLoading, setIsLoading] = React.useState(true);
  const [isAuthed, setIsAuthed] = React.useState(false);
  const [clientHierarchies, setClientHierarchies] = useState<ClientHierarchy[]>([]);
  const [expandedClients, setExpandedClients] = useState<Set<number>>(new Set());
  const [expandedParentCategories, setExpandedParentCategories] = useState<Set<number>>(new Set());
  const [hierarchyLoading, setHierarchyLoading] = useState(false);

  // Handle authentication check
  React.useEffect(() => {
    const checkAuth = async () => {
      setIsLoading(true);
      console.log('Checking auth status...');

      // Debug: Check localStorage directly
      const localStorageToken = localStorage.getItem('cms_token');
      console.log('Token in localStorage:', localStorageToken ? 'exists' : 'not found');
      console.log('Token in store:', token ? 'exists' : 'not found');

      // Check if we have a token in store
      if (!token) {
        console.log('No token in store, redirecting to login');
        navigate('/login');
        setIsLoading(false);
        return;
      }

      // We have a token, validate format
      try {
        // Simple validation - check if token looks like JWT (has two dots)
        if (typeof token !== 'string' || !token.includes('.') || token.split('.').length !== 3) {
          console.log('Invalid token format, redirecting to login');
          logout();
          // localStorage.removeItem('cms_token');
          localStorage.clear()
          navigate('/login');
          return;
        }

        console.log('Token is valid format, user is authenticated');
        setIsAuthed(true);
      } catch (error) {
        console.error('Auth validation error:', error);
        logout();
          localStorage.clear()

        navigate('/login');
      } finally {
        setIsLoading(false);
      }
    };

    checkAuth();
  }, [token, logout, navigate]);

  // Fetch client hierarchy
  const fetchClientHierarchy = async () => {
    if (hierarchyLoading) return;

    setHierarchyLoading(true);
    try {
      const clientsResponse = await clientsApi.getAll();
      const clients = clientsResponse.data || [];

      const hierarchies: ClientHierarchy[] = [];

      for (const client of clients) {
        try {
          // Fetch parent categories for this client
          const parentCategoriesResponse = await parentCategoriesApi.getByClientId(client.id.toString());
          const parentCategories = parentCategoriesResponse.data || [];

          const parentCategoryHierarchies = [];

          for (const parentCategory of parentCategories) {
            try {
              // Fetch child categories for this parent category
              const childCategoriesResponse = await categoriesApi.getByParentCategoryId(parentCategory.id.toString());
              const childCategories = childCategoriesResponse.data || [];

              parentCategoryHierarchies.push({
                parentCategory,
                childCategories
              });
            } catch (error) {
              console.error(`Error fetching child categories for parent ${parentCategory.id}:`, error);
              parentCategoryHierarchies.push({
                parentCategory,
                childCategories: []
              });
            }
          }

          hierarchies.push({
            client,
            parentCategories: parentCategoryHierarchies
          });
        } catch (error) {
          console.error(`Error fetching parent categories for client ${client.id}:`, error);
          hierarchies.push({
            client,
            parentCategories: []
          });
        }
      }

      setClientHierarchies(hierarchies);
    } catch (error) {
      console.error('Error fetching client hierarchy:', error);
    } finally {
      setHierarchyLoading(false);
    }
  };

  const toggleClientExpansion = (clientId: number) => {
    const newExpanded = new Set(expandedClients);
    if (newExpanded.has(clientId)) {
      newExpanded.delete(clientId);
    } else {
      newExpanded.add(clientId);
    }
    setExpandedClients(newExpanded);
  };

  const toggleParentCategoryExpansion = (parentCategoryId: number) => {
    const newExpanded = new Set(expandedParentCategories);
    if (newExpanded.has(parentCategoryId)) {
      newExpanded.delete(parentCategoryId);
    } else {
      newExpanded.add(parentCategoryId);
    }
    setExpandedParentCategories(newExpanded);
  };

  // Load hierarchy when component mounts and user is authenticated
  useEffect(() => {
    if (isAuthed && !hierarchyLoading && clientHierarchies.length === 0) {
      fetchClientHierarchy();
    }
  }, [isAuthed]);

  // Authentication check is now handled in the component

  // Navigation items
  const navItems = [
    {
      title: 'Dashboard',
      icon: LayoutDashboard,
      path: '/dashboard',
    },
    {
      title: 'Collections',
      icon: Database,
      path: '/collections',
    },
    {
      title: 'Categories',
      icon: Tag,
      path: '/parent-categories',
    },
    {
      title: 'Components',
      icon: Layers,
      path: '/components',
    },
    {
      title: 'Clients',
      icon: Users,
      path: '/clients',
    },
    {
      title: 'Media Library',
      icon: Boxes,
      path: '/media-library',
    },
    {
      title: 'Settings',
      icon: Settings,
      path: '/settings',
    },
  ];

  // Skip layout for auth pages
  if (location.pathname.includes('/login') || location.pathname.includes('/register')) {
    return <>{children}</>;
  }

  // Show loading state
  if (isLoading) {
    return (
      <div className="flex h-screen w-full items-center justify-center">
        <div className="text-lg">Loading...</div>
      </div>
    );
  }

  // Redirect if not authenticated
  if (!isAuthed) {
    return null;
  }

  return (
    <SidebarProvider>
      <div className="flex h-screen w-full">
        <Sidebar>
          <SidebarHeader className="flex h-14 items-center mb-8 bg-white dark:bg-slate-800 dark-glass transition-colors duration-300">
            <Link to="/dashboard" className="flex items-center gap-2 font-semibold dark-hover-lift">
              <img src="/images/cloud-logo.png" alt="CMS Logo" className="h-12 w-full mb-4 transition-transform duration-300 hover:scale-105" />
            </Link>
          </SidebarHeader>
          <SidebarContent className="p-2">
            <SidebarMenu>
              {navItems.map((item) => (
                <SidebarMenuItem key={item.path}>
                  <SidebarMenuButton asChild>
                    <Link
                      to={item.path}
                      className={`group relative overflow-hidden w-full transition-all duration-300 hover:shadow-lg hover:shadow-primary/25 dark:hover:shadow-primary/40 hover:-translate-y-1 hover:scale-105 border-2 hover:border-primary/30 dark:hover:border-primary/50 bg-gradient-to-br from-sidebar-accent/20 to-sidebar-accent/10 dark:from-sidebar-accent/30 dark:to-sidebar-accent/20 rounded-md dark-hover-lift dark-glass ${
                        location.pathname === item.path || (item.path !== '/dashboard' && location.pathname.startsWith(item.path)) ? 'bg-sidebar-accent dark:bg-sidebar-accent/80 border-primary/30 dark:border-primary/50 shadow-lg shadow-primary/25 dark:shadow-primary/40 dark-glow' : 'border-transparent'
                      }`}
                    >
                      {/* Animated background gradient */}
                      <div className="absolute inset-0 bg-gradient-to-r from-primary/5 via-purple-500/5 to-pink-500/5 dark:from-primary/10 dark:via-purple-500/10 dark:to-pink-500/10 opacity-0 group-hover:opacity-100 transition-opacity duration-500"></div>

                      {/* Shimmer effect */}
                      <div className="absolute inset-0 -translate-x-full group-hover:translate-x-full transition-transform duration-1000 bg-gradient-to-r from-transparent via-white/20 dark:via-white/10 to-transparent"></div>

                      <div className="relative z-10 flex items-center w-full">
                        <div className={`p-2 rounded-full mr-3 group-hover:scale-110 group-hover:rotate-12 transition-all duration-300 shadow-lg group-hover:shadow-xl dark:shadow-purple-500/30 dark-glow ${
                          item.title === 'Dashboard' ? 'bg-gradient-to-r from-indigo-500 to-purple-600' :
                          item.title === 'Collections' ? 'bg-gradient-to-r from-green-500 to-teal-500' :
                          item.title === 'Categories' ? 'bg-gradient-to-r from-orange-500 to-red-500' :
                          item.title === 'Components' ? 'bg-gradient-to-r from-purple-500 to-pink-500' :
                          item.title === 'Clients' ? 'bg-gradient-to-r from-cyan-500 to-blue-500' :
                          item.title === 'Media Library' ? 'bg-gradient-to-r from-blue-500 to-indigo-500' :
                          item.title === 'Settings' ? 'bg-gradient-to-r from-gray-500 to-slate-600' :
                          'bg-gradient-to-r from-purple-600 to-indigo-600'
                        }`}>
                          <item.icon className="h-4 w-4 text-white group-hover:animate-pulse" />
                        </div>
                        <span className="text-sidebar-foreground group-hover:text-white dark:group-hover:text-purple-200 transition-colors duration-300 group-hover:scale-110 transform origin-left dark-text-glow">{item.title}</span>
                      </div>



                      {/* Corner accent */}
                      <div className="absolute top-0 right-0 w-0 h-0 border-l-[20px] border-l-transparent border-t-[20px] border-t-primary/20 opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>
                    </Link>
                  </SidebarMenuButton>
                </SidebarMenuItem>
              ))}

              {/* Clients Hierarchical Menu */}
              <SidebarMenuItem>
                <SidebarMenuButton asChild>
                  <Link
                    to="/clients"
                    onClick={() => {
                      if (clientHierarchies.length === 0) {
                        fetchClientHierarchy();
                      }
                    }}
                    className={`group relative overflow-hidden w-full transition-all duration-300 hover:shadow-lg hover:shadow-primary/25 dark:hover:shadow-primary/40 hover:-translate-y-1 hover:scale-105 border-2 hover:border-primary/30 dark:hover:border-primary/50 bg-gradient-to-br from-sidebar-accent/20 to-sidebar-accent/10 dark:from-sidebar-accent/30 dark:to-sidebar-accent/20 rounded-md dark-hover-lift dark-glass ${
                      location.pathname.includes('/clients') ||
                      location.pathname.includes('/parent-categories') ||
                      location.pathname.includes('/content-types')
                        ? 'bg-sidebar-accent dark:bg-sidebar-accent/80 border-primary/30 dark:border-primary/50 shadow-lg shadow-primary/25 dark:shadow-primary/40 dark-glow' : 'border-transparent'
                    }`}
                  >
                    {/* Animated background gradient */}
                    <div className="absolute inset-0 bg-gradient-to-r from-primary/5 via-purple-500/5 to-pink-500/5 dark:from-primary/10 dark:via-purple-500/10 dark:to-pink-500/10 opacity-0 group-hover:opacity-100 transition-opacity duration-500"></div>

                    {/* Shimmer effect */}
                    <div className="absolute inset-0 -translate-x-full group-hover:translate-x-full transition-transform duration-1000 bg-gradient-to-r from-transparent via-white/20 dark:via-white/10 to-transparent"></div>

                    <div className="relative z-10 flex items-center w-full">
                      <div className="p-2 rounded-full mr-3 group-hover:scale-110 group-hover:rotate-12 transition-all duration-300 shadow-lg group-hover:shadow-xl dark:shadow-purple-500/30 bg-gradient-to-r from-indigo-500 to-purple-600 dark-glow">
                        <Database className="h-4 w-4 text-white group-hover:animate-pulse" />
                      </div>
                      <span className="text-sidebar-foreground group-hover:text-white dark:group-hover:text-purple-200 transition-colors duration-300 group-hover:scale-110 transform origin-left dark-text-glow">Clients</span>
                    </div>



                    {/* Corner accent */}
                    <div className="absolute top-0 right-0 w-0 h-0 border-l-[20px] border-l-transparent border-t-[20px] border-t-primary/20 opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>
                  </Link>
                </SidebarMenuButton>

                {/* Client Hierarchy */}
                {clientHierarchies.length > 0 && (
                  <SidebarMenuSub>
                    {clientHierarchies.map((hierarchy) => (
                      <SidebarMenuSubItem key={hierarchy.client.id}>
                        <SidebarMenuSubButton
                          onClick={() => toggleClientExpansion(hierarchy.client.id)}
                          className="group w-full justify-between hover:bg-gradient-to-r hover:from-sidebar-accent/30 hover:to-sidebar-accent/20 transition-all duration-300 hover:shadow-md hover:shadow-primary/20 hover:-translate-y-0.5 hover:scale-105 border border-transparent hover:border-primary/30 rounded-md"
                        >
                          <div className="flex items-center">
                            <div className="p-1.5 rounded-full mr-2 group-hover:scale-110 group-hover:rotate-6 transition-all duration-300 bg-sidebar-accent/40 group-hover:bg-sidebar-accent/60">
                              <Building className="h-3 w-3 text-sidebar-foreground group-hover:animate-pulse" />
                            </div>
                            <span className="text-sm text-sidebar-foreground group-hover:text-white transition-colors duration-300">{hierarchy.client.name}</span>
                          </div>
                          <div className="group-hover:scale-110 transition-transform duration-300">
                            {expandedClients.has(hierarchy.client.id) ? (
                              <ChevronDown className="h-3 w-3 text-sidebar-foreground group-hover:text-white" />
                            ) : (
                              <ChevronRight className="h-3 w-3 text-sidebar-foreground group-hover:text-white" />
                            )}
                          </div>
                        </SidebarMenuSubButton>

                        {/* Parent Categories */}
                        {expandedClients.has(hierarchy.client.id) && (
                          <div className="ml-4 mt-1 space-y-1">
                            {hierarchy.parentCategories.map((parentCategoryData) => (
                              <div key={parentCategoryData.parentCategory.id}>
                                <SidebarMenuSubButton
                                  onClick={() => toggleParentCategoryExpansion(parentCategoryData.parentCategory.id)}
                                  className="group w-full justify-between text-xs hover:bg-gradient-to-r hover:from-sidebar-accent/40 hover:to-sidebar-accent/30 transition-all duration-300 hover:shadow-sm hover:shadow-primary/20 hover:-translate-y-0.5 hover:scale-105 border border-transparent hover:border-primary/30 rounded-md"
                                >
                                  <div className="flex items-center">
                                    <div className="p-1 rounded-full mr-2 group-hover:scale-110 group-hover:rotate-3 transition-all duration-300 bg-sidebar-accent/50 group-hover:bg-sidebar-accent/70">
                                      {expandedParentCategories.has(parentCategoryData.parentCategory.id) ? (
                                        <FolderOpen className="h-2.5 w-2.5 text-sidebar-foreground group-hover:animate-pulse" />
                                      ) : (
                                        <Folder className="h-2.5 w-2.5 text-sidebar-foreground group-hover:animate-pulse" />
                                      )}
                                    </div>
                                    <span className="text-sidebar-foreground group-hover:text-white transition-colors duration-300">{parentCategoryData.parentCategory.categoryName}</span>
                                  </div>
                                  <div className="group-hover:scale-110 transition-transform duration-300">
                                    {expandedParentCategories.has(parentCategoryData.parentCategory.id) ? (
                                      <ChevronDown className="h-2 w-2 text-sidebar-foreground group-hover:text-white" />
                                    ) : (
                                      <ChevronRight className="h-2 w-2 text-sidebar-foreground group-hover:text-white" />
                                    )}
                                  </div>
                                </SidebarMenuSubButton>

                                {/* Child Categories */}
                                {expandedParentCategories.has(parentCategoryData.parentCategory.id) && (
                                  <div className="ml-4 mt-1 space-y-1">
                                    {parentCategoryData.childCategories.map((childCategory) => (
                                      <SidebarMenuSubButton
                                        key={childCategory.id}
                                        asChild
                                        className="text-xs"
                                      >
                                        <Link
                                          to={`/content-types/category/${childCategory.id}`}
                                          className="group flex items-center hover:bg-gradient-to-r hover:from-sidebar-accent/50 hover:to-sidebar-accent/40 transition-all duration-300 hover:shadow-sm hover:shadow-primary/20 hover:-translate-y-0.5 hover:scale-105 border border-transparent hover:border-primary/30 rounded-md p-1"
                                        >
                                          <div className="p-0.5 rounded-full mr-2 group-hover:scale-110 group-hover:rotate-2 transition-all duration-300 bg-sidebar-accent/60 group-hover:bg-sidebar-accent/80">
                                            <Folder className="h-2 w-2 text-sidebar-foreground group-hover:animate-pulse" />
                                          </div>
                                          <span className="text-sidebar-foreground group-hover:text-white transition-colors duration-300">{childCategory.categoryName}</span>
                                        </Link>
                                      </SidebarMenuSubButton>
                                    ))}
                                  </div>
                                )}
                              </div>
                            ))}
                          </div>
                        )}
                      </SidebarMenuSubItem>
                    ))}
                  </SidebarMenuSub>
                )}
              </SidebarMenuItem>
            </SidebarMenu>
          </SidebarContent>
          <SidebarFooter className="mt-auto p-4 border-t border-sidebar-border dark:border-sidebar-border/50 dark-glass">
            <div className="flex items-center justify-center">
              <span className="text-sm text-white dark:text-purple-200 dark-text-glow transition-colors duration-300">© {new Date().getFullYear()} R-CMS</span>
            </div>
          </SidebarFooter>
        </Sidebar>
        <div className="flex-1 flex flex-col min-h-0 bg-transparent relative gradient-pattern">
          <Header />
          <main className="flex-1 overflow-auto p-6 bg-transparent relative z-10">{children}</main>
        </div>
      </div>
    </SidebarProvider>
  );
}
