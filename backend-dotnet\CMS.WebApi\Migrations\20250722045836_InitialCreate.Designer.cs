// <auto-generated />
using System;
using CMS.WebApi.Data;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Infrastructure;
using Microsoft.EntityFrameworkCore.Migrations;
using Microsoft.EntityFrameworkCore.Storage.ValueConversion;
using Npgsql.EntityFrameworkCore.PostgreSQL.Metadata;

#nullable disable

namespace CMS.WebApi.Migrations
{
    [DbContext(typeof(CmsDbContext))]
    [Migration("20250722045836_InitialCreate")]
    partial class InitialCreate
    {
        /// <inheritdoc />
        protected override void BuildTargetModel(ModelBuilder modelBuilder)
        {
#pragma warning disable 612, 618
            modelBuilder
                .HasAnnotation("ProductVersion", "8.0.0")
                .HasAnnotation("Relational:MaxIdentifierLength", 63);

            NpgsqlModelBuilderExtensions.UseIdentityByDefaultColumns(modelBuilder);

            modelBuilder.HasSequence("cms_api_token_seq")
                .StartsAt(100L);

            modelBuilder.HasSequence<int>("cms_category_seq")
                .StartsAt(100L);

            modelBuilder.HasSequence<int>("cms_client_seq")
                .StartsAt(100L);

            modelBuilder.HasSequence<int>("cms_collection_component_seq")
                .StartsAt(100L);

            modelBuilder.HasSequence<int>("cms_collection_field_seq")
                .StartsAt(100L);

            modelBuilder.HasSequence<int>("cms_collection_listing_seq")
                .StartsAt(100L);

            modelBuilder.HasSequence<int>("cms_component_component_seq")
                .StartsAt(100L);

            modelBuilder.HasSequence<int>("cms_component_field_seq")
                .StartsAt(100L);

            modelBuilder.HasSequence<int>("cms_component_listing_seq")
                .StartsAt(100L);

            modelBuilder.HasSequence<int>("cms_config_type_seq")
                .StartsAt(100L);

            modelBuilder.HasSequence<int>("cms_content_entry_seq")
                .StartsAt(100L);

            modelBuilder.HasSequence<int>("cms_field_config_seq")
                .StartsAt(100L);

            modelBuilder.HasSequence<int>("cms_field_type_seq")
                .StartsAt(100L);

            modelBuilder.HasSequence<int>("cms_media_folder_seq")
                .StartsAt(100L);

            modelBuilder.HasSequence<int>("cms_media_seq")
                .StartsAt(100L);

            modelBuilder.HasSequence("cms_user_seq")
                .StartsAt(100L);

            modelBuilder.Entity("CMS.WebApi.Models.Entities.ApiToken", b =>
                {
                    b.Property<long>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("bigint")
                        .HasColumnName("id");

                    NpgsqlPropertyBuilderExtensions.UseIdentityByDefaultColumn(b.Property<long>("Id"));

                    b.Property<DateTime>("CreatedAt")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("CreatedBy")
                        .HasMaxLength(50)
                        .HasColumnType("character varying(50)");

                    b.Property<string>("Description")
                        .HasMaxLength(200)
                        .HasColumnType("character varying(200)")
                        .HasColumnName("description");

                    b.Property<DateTime>("ExpiresAt")
                        .HasColumnType("timestamp with time zone")
                        .HasColumnName("expires_at");

                    b.Property<bool>("IsActive")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("boolean")
                        .HasDefaultValue(true)
                        .HasColumnName("is_active");

                    b.Property<DateTime?>("LastUsedAt")
                        .HasColumnType("timestamp with time zone")
                        .HasColumnName("last_used_at");

                    b.Property<DateTime?>("ModifiedAt")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("ModifiedBy")
                        .HasMaxLength(50)
                        .HasColumnType("character varying(50)");

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasMaxLength(50)
                        .HasColumnType("character varying(50)")
                        .HasColumnName("name");

                    b.Property<string>("TokenValue")
                        .IsRequired()
                        .HasMaxLength(255)
                        .HasColumnType("character varying(255)")
                        .HasColumnName("token_value");

                    b.Property<long>("UserId")
                        .HasColumnType("bigint")
                        .HasColumnName("user_id");

                    b.HasKey("Id");

                    b.HasIndex("TokenValue")
                        .IsUnique();

                    b.HasIndex("UserId");

                    b.ToTable("api_tokens", (string)null);
                });

            modelBuilder.Entity("CMS.WebApi.Models.Entities.Category", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("integer")
                        .HasColumnName("id");

                    NpgsqlPropertyBuilderExtensions.UseIdentityByDefaultColumn(b.Property<int>("Id"));

                    b.Property<string>("CategoryName")
                        .IsRequired()
                        .HasMaxLength(255)
                        .HasColumnType("character varying(255)")
                        .HasColumnName("category_name");

                    b.Property<int?>("ClientId")
                        .HasColumnType("integer")
                        .HasColumnName("client_id");

                    b.Property<DateTime>("CreatedAt")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("CreatedBy")
                        .HasMaxLength(50)
                        .HasColumnType("character varying(50)");

                    b.Property<DateTime?>("ModifiedAt")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("ModifiedBy")
                        .HasMaxLength(50)
                        .HasColumnType("character varying(50)");

                    b.Property<int?>("ParentCategoryId")
                        .HasColumnType("integer")
                        .HasColumnName("parent_category_id");

                    b.HasKey("Id");

                    b.HasIndex("ClientId");

                    b.HasIndex("ParentCategoryId");

                    b.ToTable("category", (string)null);
                });

            modelBuilder.Entity("CMS.WebApi.Models.Entities.Client", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("integer")
                        .HasColumnName("id");

                    NpgsqlPropertyBuilderExtensions.UseIdentityByDefaultColumn(b.Property<int>("Id"));

                    b.Property<DateTime>("CreatedAt")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("CreatedBy")
                        .HasMaxLength(50)
                        .HasColumnType("character varying(50)");

                    b.Property<DateTime?>("ModifiedAt")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("ModifiedBy")
                        .HasMaxLength(50)
                        .HasColumnType("character varying(50)");

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasMaxLength(255)
                        .HasColumnType("character varying(255)")
                        .HasColumnName("name");

                    b.HasKey("Id");

                    b.ToTable("clients", (string)null);
                });

            modelBuilder.Entity("CMS.WebApi.Models.Entities.CollectionComponent", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("integer")
                        .HasColumnName("id");

                    NpgsqlPropertyBuilderExtensions.UseIdentityByDefaultColumn(b.Property<int>("Id"));

                    b.Property<string>("AdditionalInfo")
                        .HasColumnType("text")
                        .HasColumnName("additional_info");

                    b.Property<string>("AdditionalInfoImage")
                        .HasMaxLength(255)
                        .HasColumnType("character varying(255)")
                        .HasColumnName("additional_info_image");

                    b.Property<int>("CollectionId")
                        .HasColumnType("integer")
                        .HasColumnName("collection_id");

                    b.Property<int>("ComponentId")
                        .HasColumnType("integer")
                        .HasColumnName("component_id");

                    b.Property<DateTime>("CreatedAt")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("CreatedBy")
                        .HasMaxLength(50)
                        .HasColumnType("character varying(50)");

                    b.Property<string>("DisplayName")
                        .HasMaxLength(255)
                        .HasColumnType("character varying(255)")
                        .HasColumnName("display_name");

                    b.Property<int?>("DisplayPreference")
                        .HasColumnType("integer")
                        .HasColumnName("display_preference");

                    b.Property<bool>("IsActive")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("boolean")
                        .HasDefaultValue(true)
                        .HasColumnName("is_active");

                    b.Property<bool>("IsRepeatable")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("boolean")
                        .HasDefaultValue(false)
                        .HasColumnName("is_repeatable");

                    b.Property<int?>("MaxRepeatOccurrences")
                        .HasColumnType("integer")
                        .HasColumnName("max_repeat_occurrences");

                    b.Property<int?>("MinRepeatOccurrences")
                        .HasColumnType("integer")
                        .HasColumnName("min_repeat_occurrences");

                    b.Property<DateTime?>("ModifiedAt")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("ModifiedBy")
                        .HasMaxLength(50)
                        .HasColumnType("character varying(50)");

                    b.Property<string>("Name")
                        .HasMaxLength(255)
                        .HasColumnType("character varying(255)")
                        .HasColumnName("name");

                    b.HasKey("Id");

                    b.HasIndex("CollectionId");

                    b.HasIndex("ComponentId");

                    b.ToTable("collection_components", (string)null);
                });

            modelBuilder.Entity("CMS.WebApi.Models.Entities.CollectionField", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("integer")
                        .HasColumnName("id");

                    NpgsqlPropertyBuilderExtensions.UseIdentityByDefaultColumn(b.Property<int>("Id"));

                    b.Property<string>("AdditionalInformation")
                        .HasColumnType("text")
                        .HasColumnName("additional_information");

                    b.Property<int>("CollectionId")
                        .HasColumnType("integer")
                        .HasColumnName("collection_id");

                    b.Property<DateTime>("CreatedAt")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("CreatedBy")
                        .HasMaxLength(50)
                        .HasColumnType("character varying(50)");

                    b.Property<int?>("DependentOnId")
                        .HasColumnType("integer")
                        .HasColumnName("dependent_on");

                    b.Property<int?>("DisplayPreference")
                        .HasColumnType("integer")
                        .HasColumnName("display_preference");

                    b.Property<int>("FieldTypeId")
                        .HasColumnType("integer")
                        .HasColumnName("field_type_id");

                    b.Property<DateTime?>("ModifiedAt")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("ModifiedBy")
                        .HasMaxLength(50)
                        .HasColumnType("character varying(50)");

                    b.HasKey("Id");

                    b.HasIndex("CollectionId");

                    b.HasIndex("DependentOnId");

                    b.HasIndex("FieldTypeId");

                    b.ToTable("collection_fields", (string)null);
                });

            modelBuilder.Entity("CMS.WebApi.Models.Entities.CollectionFieldConfig", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("integer")
                        .HasColumnName("id");

                    NpgsqlPropertyBuilderExtensions.UseIdentityByDefaultColumn(b.Property<int>("Id"));

                    b.Property<int>("CollectionFieldId")
                        .HasColumnType("integer")
                        .HasColumnName("collection_field_id");

                    b.Property<string>("ConfigValue")
                        .HasMaxLength(255)
                        .HasColumnType("character varying(255)")
                        .HasColumnName("config_value");

                    b.Property<DateTime>("CreatedAt")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("CreatedBy")
                        .HasMaxLength(50)
                        .HasColumnType("character varying(50)");

                    b.Property<int>("FieldConfigId")
                        .HasColumnType("integer")
                        .HasColumnName("field_config_id");

                    b.Property<bool>("IsActive")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("boolean")
                        .HasDefaultValue(true)
                        .HasColumnName("is_active");

                    b.Property<DateTime?>("ModifiedAt")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("ModifiedBy")
                        .HasMaxLength(50)
                        .HasColumnType("character varying(50)");

                    b.HasKey("Id");

                    b.HasIndex("CollectionFieldId");

                    b.HasIndex("FieldConfigId");

                    b.ToTable("collection_field_config", (string)null);
                });

            modelBuilder.Entity("CMS.WebApi.Models.Entities.CollectionListing", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("integer")
                        .HasColumnName("id");

                    NpgsqlPropertyBuilderExtensions.UseIdentityByDefaultColumn(b.Property<int>("Id"));

                    b.Property<string>("AdditionalInformation")
                        .HasColumnType("text")
                        .HasColumnName("additional_information");

                    b.Property<int>("CategoryId")
                        .HasColumnType("integer")
                        .HasColumnName("category_id");

                    b.Property<string>("CollectionApiId")
                        .IsRequired()
                        .HasMaxLength(255)
                        .HasColumnType("character varying(255)")
                        .HasColumnName("collection_api_id");

                    b.Property<string>("CollectionDesc")
                        .HasColumnType("text")
                        .HasColumnName("collection_desc");

                    b.Property<string>("CollectionName")
                        .IsRequired()
                        .HasMaxLength(255)
                        .HasColumnType("character varying(255)")
                        .HasColumnName("collection_name");

                    b.Property<DateTime>("CreatedAt")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("CreatedBy")
                        .HasMaxLength(50)
                        .HasColumnType("character varying(50)");

                    b.Property<string>("DisclaimerText")
                        .HasColumnType("text")
                        .HasColumnName("disclaimer_text");

                    b.Property<DateTime?>("ModifiedAt")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("ModifiedBy")
                        .HasMaxLength(50)
                        .HasColumnType("character varying(50)");

                    b.HasKey("Id");

                    b.HasIndex("CategoryId");

                    b.ToTable("collection_listing", (string)null);
                });

            modelBuilder.Entity("CMS.WebApi.Models.Entities.ComponentComponent", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("integer")
                        .HasColumnName("id");

                    NpgsqlPropertyBuilderExtensions.UseIdentityByDefaultColumn(b.Property<int>("Id"));

                    b.Property<string>("AdditionalInformation")
                        .HasColumnType("text")
                        .HasColumnName("additional_information");

                    b.Property<int>("ChildComponentId")
                        .HasColumnType("integer")
                        .HasColumnName("child_component_id");

                    b.Property<DateTime>("CreatedAt")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("CreatedBy")
                        .HasMaxLength(50)
                        .HasColumnType("character varying(50)");

                    b.Property<int?>("DisplayPreference")
                        .HasColumnType("integer")
                        .HasColumnName("display_preference");

                    b.Property<bool>("IsActive")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("boolean")
                        .HasDefaultValue(true)
                        .HasColumnName("is_active");

                    b.Property<bool>("IsRepeatable")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("boolean")
                        .HasDefaultValue(false)
                        .HasColumnName("is_repeatable");

                    b.Property<DateTime?>("ModifiedAt")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("ModifiedBy")
                        .HasMaxLength(50)
                        .HasColumnType("character varying(50)");

                    b.Property<int>("ParentComponentId")
                        .HasColumnType("integer")
                        .HasColumnName("parent_component_id");

                    b.HasKey("Id");

                    b.HasIndex("ChildComponentId");

                    b.HasIndex("ParentComponentId");

                    b.ToTable("component_components", (string)null);
                });

            modelBuilder.Entity("CMS.WebApi.Models.Entities.ComponentField", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("integer")
                        .HasColumnName("id");

                    NpgsqlPropertyBuilderExtensions.UseIdentityByDefaultColumn(b.Property<int>("Id"));

                    b.Property<string>("AdditionalInformation")
                        .HasColumnType("text")
                        .HasColumnName("additional_information");

                    b.Property<int>("ComponentId")
                        .HasColumnType("integer")
                        .HasColumnName("component_id");

                    b.Property<DateTime>("CreatedAt")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("CreatedBy")
                        .HasMaxLength(50)
                        .HasColumnType("character varying(50)");

                    b.Property<int?>("DependentOnId")
                        .HasColumnType("integer")
                        .HasColumnName("dependent_on");

                    b.Property<int?>("DisplayPreference")
                        .HasColumnType("integer")
                        .HasColumnName("display_preference");

                    b.Property<int>("FieldTypeId")
                        .HasColumnType("integer")
                        .HasColumnName("field_type_id");

                    b.Property<DateTime?>("ModifiedAt")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("ModifiedBy")
                        .HasMaxLength(50)
                        .HasColumnType("character varying(50)");

                    b.HasKey("Id");

                    b.HasIndex("ComponentId");

                    b.HasIndex("DependentOnId");

                    b.HasIndex("FieldTypeId");

                    b.ToTable("component_fields", (string)null);
                });

            modelBuilder.Entity("CMS.WebApi.Models.Entities.ComponentFieldConfig", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("integer")
                        .HasColumnName("id");

                    NpgsqlPropertyBuilderExtensions.UseIdentityByDefaultColumn(b.Property<int>("Id"));

                    b.Property<int>("ComponentFieldId")
                        .HasColumnType("integer")
                        .HasColumnName("component_field_id");

                    b.Property<string>("ConfigValue")
                        .HasMaxLength(255)
                        .HasColumnType("character varying(255)")
                        .HasColumnName("config_value");

                    b.Property<DateTime>("CreatedAt")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("CreatedBy")
                        .HasMaxLength(50)
                        .HasColumnType("character varying(50)");

                    b.Property<int>("FieldConfigId")
                        .HasColumnType("integer")
                        .HasColumnName("field_config_id");

                    b.Property<bool>("IsActive")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("boolean")
                        .HasDefaultValue(true)
                        .HasColumnName("is_active");

                    b.Property<DateTime?>("ModifiedAt")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("ModifiedBy")
                        .HasMaxLength(50)
                        .HasColumnType("character varying(50)");

                    b.HasKey("Id");

                    b.HasIndex("ComponentFieldId");

                    b.HasIndex("FieldConfigId");

                    b.ToTable("component_field_config", (string)null);
                });

            modelBuilder.Entity("CMS.WebApi.Models.Entities.ComponentListing", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("integer")
                        .HasColumnName("id");

                    NpgsqlPropertyBuilderExtensions.UseIdentityByDefaultColumn(b.Property<int>("Id"));

                    b.Property<string>("AdditionalInfoImage")
                        .HasMaxLength(255)
                        .HasColumnType("character varying(255)")
                        .HasColumnName("additional_info_image");

                    b.Property<string>("AdditionalInformation")
                        .HasColumnType("text")
                        .HasColumnName("additional_information");

                    b.Property<int?>("CategoryId")
                        .HasColumnType("integer");

                    b.Property<string>("ComponentApiId")
                        .HasMaxLength(255)
                        .HasColumnType("character varying(255)")
                        .HasColumnName("component_api_id");

                    b.Property<string>("ComponentDisplayName")
                        .HasMaxLength(255)
                        .HasColumnType("character varying(255)")
                        .HasColumnName("component_display_name");

                    b.Property<string>("ComponentName")
                        .IsRequired()
                        .HasMaxLength(255)
                        .HasColumnType("character varying(255)")
                        .HasColumnName("component_name");

                    b.Property<DateTime>("CreatedAt")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("CreatedBy")
                        .HasMaxLength(50)
                        .HasColumnType("character varying(50)");

                    b.Property<string>("GetUrl")
                        .HasColumnType("text")
                        .HasColumnName("get_url");

                    b.Property<bool>("IsActive")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("boolean")
                        .HasDefaultValue(true)
                        .HasColumnName("is_active");

                    b.Property<DateTime?>("ModifiedAt")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("ModifiedBy")
                        .HasMaxLength(50)
                        .HasColumnType("character varying(50)");

                    b.Property<string>("PostUrl")
                        .HasColumnType("text")
                        .HasColumnName("post_url");

                    b.Property<string>("UpdateUrl")
                        .HasColumnType("text")
                        .HasColumnName("update_url");

                    b.HasKey("Id");

                    b.HasIndex("CategoryId");

                    b.HasIndex("ComponentName")
                        .IsUnique();

                    b.ToTable("component_listing", (string)null);
                });

            modelBuilder.Entity("CMS.WebApi.Models.Entities.ConfigType", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("integer")
                        .HasColumnName("id");

                    NpgsqlPropertyBuilderExtensions.UseIdentityByDefaultColumn(b.Property<int>("Id"));

                    b.Property<string>("AdditionalInfo")
                        .HasColumnType("text")
                        .HasColumnName("additional_info");

                    b.Property<string>("ConfigTypeDesc")
                        .HasColumnType("text")
                        .HasColumnName("config_type_desc");

                    b.Property<string>("ConfigTypeName")
                        .IsRequired()
                        .HasMaxLength(255)
                        .HasColumnType("character varying(255)")
                        .HasColumnName("config_type_name");

                    b.Property<DateTime>("CreatedAt")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("CreatedBy")
                        .HasMaxLength(50)
                        .HasColumnType("character varying(50)");

                    b.Property<string>("DisclaimerText")
                        .HasColumnType("text")
                        .HasColumnName("disclaimer_text");

                    b.Property<string>("DisplayName")
                        .HasMaxLength(255)
                        .HasColumnType("character varying(255)")
                        .HasColumnName("display_name");

                    b.Property<bool>("IsActive")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("boolean")
                        .HasDefaultValue(true)
                        .HasColumnName("is_active");

                    b.Property<DateTime?>("ModifiedAt")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("ModifiedBy")
                        .HasMaxLength(50)
                        .HasColumnType("character varying(50)");

                    b.Property<string>("PlaceholderText")
                        .HasColumnType("text")
                        .HasColumnName("placeholder_text");

                    b.HasKey("Id");

                    b.HasIndex("ConfigTypeName")
                        .IsUnique();

                    b.ToTable("config_types", (string)null);
                });

            modelBuilder.Entity("CMS.WebApi.Models.Entities.ContentEntry", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("integer")
                        .HasColumnName("id");

                    NpgsqlPropertyBuilderExtensions.UseIdentityByDefaultColumn(b.Property<int>("Id"));

                    b.Property<int>("CollectionId")
                        .HasColumnType("integer")
                        .HasColumnName("collection_id");

                    b.Property<DateTime>("CreatedAt")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("CreatedBy")
                        .HasMaxLength(50)
                        .HasColumnType("character varying(50)");

                    b.Property<string>("DataJson")
                        .HasColumnType("jsonb")
                        .HasColumnName("data_json");

                    b.Property<DateTime?>("ModifiedAt")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("ModifiedBy")
                        .HasMaxLength(50)
                        .HasColumnType("character varying(50)");

                    b.HasKey("Id");

                    b.HasIndex("CollectionId");

                    b.ToTable("content_entries", (string)null);
                });

            modelBuilder.Entity("CMS.WebApi.Models.Entities.FieldConfig", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("integer")
                        .HasColumnName("id");

                    NpgsqlPropertyBuilderExtensions.UseIdentityByDefaultColumn(b.Property<int>("Id"));

                    b.Property<string>("ConfigName")
                        .HasMaxLength(255)
                        .HasColumnType("character varying(255)")
                        .HasColumnName("config_name");

                    b.Property<int?>("ConfigTypeId")
                        .HasColumnType("integer")
                        .HasColumnName("config_type_id");

                    b.Property<DateTime>("CreatedAt")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("CreatedBy")
                        .HasMaxLength(50)
                        .HasColumnType("character varying(50)");

                    b.Property<int?>("FieldTypeId")
                        .HasColumnType("integer")
                        .HasColumnName("field_type_id");

                    b.Property<bool>("IsActive")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("boolean")
                        .HasDefaultValue(true)
                        .HasColumnName("is_active");

                    b.Property<DateTime?>("ModifiedAt")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("ModifiedBy")
                        .HasMaxLength(50)
                        .HasColumnType("character varying(50)");

                    b.Property<string>("ValueType")
                        .HasMaxLength(50)
                        .HasColumnType("character varying(50)")
                        .HasColumnName("value_type");

                    b.HasKey("Id");

                    b.HasIndex("ConfigTypeId");

                    b.HasIndex("FieldTypeId");

                    b.ToTable("field_configs", (string)null);
                });

            modelBuilder.Entity("CMS.WebApi.Models.Entities.FieldType", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("integer")
                        .HasColumnName("id");

                    NpgsqlPropertyBuilderExtensions.UseIdentityByDefaultColumn(b.Property<int>("Id"));

                    b.Property<DateTime>("CreatedAt")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("CreatedBy")
                        .HasMaxLength(50)
                        .HasColumnType("character varying(50)");

                    b.Property<string>("DisplayName")
                        .HasMaxLength(255)
                        .HasColumnType("character varying(255)")
                        .HasColumnName("display_name");

                    b.Property<string>("FieldTypeDesc")
                        .HasColumnType("text")
                        .HasColumnName("field_type_desc");

                    b.Property<string>("FieldTypeName")
                        .IsRequired()
                        .HasMaxLength(255)
                        .HasColumnType("character varying(255)")
                        .HasColumnName("field_type_name");

                    b.Property<string>("HelpText")
                        .HasColumnType("text")
                        .HasColumnName("help_text");

                    b.Property<bool>("IsActive")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("boolean")
                        .HasDefaultValue(true)
                        .HasColumnName("is_active");

                    b.Property<string>("LogoImagePath")
                        .HasMaxLength(255)
                        .HasColumnType("character varying(255)")
                        .HasColumnName("logo_image_path");

                    b.Property<DateTime?>("ModifiedAt")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("ModifiedBy")
                        .HasMaxLength(50)
                        .HasColumnType("character varying(50)");

                    b.HasKey("Id");

                    b.HasIndex("FieldTypeName")
                        .IsUnique();

                    b.ToTable("field_types", (string)null);
                });

            modelBuilder.Entity("CMS.WebApi.Models.Entities.Media", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("integer")
                        .HasColumnName("id");

                    NpgsqlPropertyBuilderExtensions.UseIdentityByDefaultColumn(b.Property<int>("Id"));

                    b.Property<string>("AltText")
                        .HasColumnType("text")
                        .HasColumnName("alt_text");

                    b.Property<DateTime>("CreatedAt")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("CreatedBy")
                        .HasMaxLength(50)
                        .HasColumnType("character varying(50)");

                    b.Property<string>("Description")
                        .HasColumnType("text")
                        .HasColumnName("description");

                    b.Property<int?>("Duration")
                        .HasColumnType("integer")
                        .HasColumnName("duration");

                    b.Property<string>("FileName")
                        .IsRequired()
                        .HasMaxLength(255)
                        .HasColumnType("character varying(255)")
                        .HasColumnName("file_name");

                    b.Property<string>("FilePath")
                        .IsRequired()
                        .HasColumnType("text")
                        .HasColumnName("file_path");

                    b.Property<long>("FileSize")
                        .HasColumnType("bigint")
                        .HasColumnName("file_size");

                    b.Property<string>("FileType")
                        .IsRequired()
                        .HasMaxLength(100)
                        .HasColumnType("character varying(100)")
                        .HasColumnName("file_type");

                    b.Property<string>("FileUrl")
                        .HasColumnType("text");

                    b.Property<int?>("FolderId")
                        .HasColumnType("integer")
                        .HasColumnName("folder_id");

                    b.Property<int?>("Height")
                        .HasColumnType("integer")
                        .HasColumnName("height");

                    b.Property<bool>("IsPublic")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("boolean")
                        .HasDefaultValue(false)
                        .HasColumnName("is_public");

                    b.Property<int?>("MediaFolderId")
                        .HasColumnType("integer");

                    b.Property<DateTime?>("ModifiedAt")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("ModifiedBy")
                        .HasMaxLength(50)
                        .HasColumnType("character varying(50)");

                    b.Property<string>("OriginalFileName")
                        .IsRequired()
                        .HasMaxLength(255)
                        .HasColumnType("character varying(255)")
                        .HasColumnName("original_file_name");

                    b.Property<string>("PublicUrl")
                        .HasColumnType("text")
                        .HasColumnName("public_url");

                    b.Property<string>("ShareToken")
                        .HasColumnType("text")
                        .HasColumnName("share_token");

                    b.Property<long?>("UserId")
                        .HasColumnType("bigint");

                    b.Property<int?>("Width")
                        .HasColumnType("integer")
                        .HasColumnName("width");

                    b.HasKey("Id");

                    b.HasIndex("FolderId");

                    b.HasIndex("MediaFolderId");

                    b.HasIndex("UserId");

                    b.ToTable("media", (string)null);
                });

            modelBuilder.Entity("CMS.WebApi.Models.Entities.MediaFolder", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("integer")
                        .HasColumnName("id");

                    NpgsqlPropertyBuilderExtensions.UseIdentityByDefaultColumn(b.Property<int>("Id"));

                    b.Property<DateTime>("CreatedAt")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("CreatedBy")
                        .HasMaxLength(50)
                        .HasColumnType("character varying(50)");

                    b.Property<string>("Description")
                        .HasColumnType("text")
                        .HasColumnName("description");

                    b.Property<string>("FolderName")
                        .IsRequired()
                        .HasMaxLength(100)
                        .HasColumnType("character varying(100)")
                        .HasColumnName("folder_name");

                    b.Property<DateTime?>("ModifiedAt")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("ModifiedBy")
                        .HasMaxLength(50)
                        .HasColumnType("character varying(50)");

                    b.Property<int?>("ParentId")
                        .HasColumnType("integer")
                        .HasColumnName("parent_id");

                    b.Property<long?>("UserId")
                        .HasColumnType("bigint")
                        .HasColumnName("user_id");

                    b.HasKey("Id");

                    b.HasIndex("ParentId");

                    b.HasIndex("UserId");

                    b.ToTable("media_folders", (string)null);
                });

            modelBuilder.Entity("CMS.WebApi.Models.Entities.User", b =>
                {
                    b.Property<long>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("bigint")
                        .HasColumnName("id");

                    NpgsqlPropertyBuilderExtensions.UseIdentityByDefaultColumn(b.Property<long>("Id"));

                    b.Property<int>("AccessFailedCount")
                        .HasColumnType("integer");

                    b.Property<string>("ConcurrencyStamp")
                        .IsConcurrencyToken()
                        .HasColumnType("text");

                    b.Property<DateTime>("CreatedAt")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("CreatedBy")
                        .HasColumnType("text");

                    b.Property<string>("Email")
                        .HasMaxLength(100)
                        .HasColumnType("character varying(100)")
                        .HasColumnName("email");

                    b.Property<bool>("EmailConfirmed")
                        .HasColumnType("boolean");

                    b.Property<bool>("IsActive")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("boolean")
                        .HasDefaultValue(true)
                        .HasColumnName("is_active");

                    b.Property<bool>("LockoutEnabled")
                        .HasColumnType("boolean");

                    b.Property<DateTimeOffset?>("LockoutEnd")
                        .HasColumnType("timestamp with time zone");

                    b.Property<DateTime?>("ModifiedAt")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("ModifiedBy")
                        .HasColumnType("text");

                    b.Property<string>("NormalizedEmail")
                        .HasMaxLength(256)
                        .HasColumnType("character varying(256)");

                    b.Property<string>("NormalizedUserName")
                        .HasMaxLength(256)
                        .HasColumnType("character varying(256)");

                    b.Property<string>("PasswordHash")
                        .HasColumnType("text")
                        .HasColumnName("password");

                    b.Property<string>("PhoneNumber")
                        .HasColumnType("text");

                    b.Property<bool>("PhoneNumberConfirmed")
                        .HasColumnType("boolean");

                    b.Property<string>("SecurityStamp")
                        .HasColumnType("text");

                    b.Property<bool>("TwoFactorEnabled")
                        .HasColumnType("boolean");

                    b.Property<string>("UserName")
                        .HasMaxLength(50)
                        .HasColumnType("character varying(50)")
                        .HasColumnName("username");

                    b.HasKey("Id");

                    b.HasIndex("NormalizedEmail")
                        .HasDatabaseName("EmailIndex");

                    b.HasIndex("NormalizedUserName")
                        .IsUnique()
                        .HasDatabaseName("UserNameIndex");

                    b.ToTable("users", (string)null);
                });

            modelBuilder.Entity("Microsoft.AspNetCore.Identity.IdentityRole<long>", b =>
                {
                    b.Property<long>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("bigint");

                    NpgsqlPropertyBuilderExtensions.UseIdentityByDefaultColumn(b.Property<long>("Id"));

                    b.Property<string>("ConcurrencyStamp")
                        .IsConcurrencyToken()
                        .HasColumnType("text");

                    b.Property<string>("Name")
                        .HasMaxLength(256)
                        .HasColumnType("character varying(256)");

                    b.Property<string>("NormalizedName")
                        .HasMaxLength(256)
                        .HasColumnType("character varying(256)");

                    b.HasKey("Id");

                    b.HasIndex("NormalizedName")
                        .IsUnique()
                        .HasDatabaseName("RoleNameIndex");

                    b.ToTable("AspNetRoles", (string)null);
                });

            modelBuilder.Entity("Microsoft.AspNetCore.Identity.IdentityRoleClaim<long>", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("integer");

                    NpgsqlPropertyBuilderExtensions.UseIdentityByDefaultColumn(b.Property<int>("Id"));

                    b.Property<string>("ClaimType")
                        .HasColumnType("text");

                    b.Property<string>("ClaimValue")
                        .HasColumnType("text");

                    b.Property<long>("RoleId")
                        .HasColumnType("bigint");

                    b.HasKey("Id");

                    b.HasIndex("RoleId");

                    b.ToTable("AspNetRoleClaims", (string)null);
                });

            modelBuilder.Entity("Microsoft.AspNetCore.Identity.IdentityUserClaim<long>", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("integer");

                    NpgsqlPropertyBuilderExtensions.UseIdentityByDefaultColumn(b.Property<int>("Id"));

                    b.Property<string>("ClaimType")
                        .HasColumnType("text");

                    b.Property<string>("ClaimValue")
                        .HasColumnType("text");

                    b.Property<long>("UserId")
                        .HasColumnType("bigint");

                    b.HasKey("Id");

                    b.HasIndex("UserId");

                    b.ToTable("AspNetUserClaims", (string)null);
                });

            modelBuilder.Entity("Microsoft.AspNetCore.Identity.IdentityUserLogin<long>", b =>
                {
                    b.Property<string>("LoginProvider")
                        .HasColumnType("text");

                    b.Property<string>("ProviderKey")
                        .HasColumnType("text");

                    b.Property<string>("ProviderDisplayName")
                        .HasColumnType("text");

                    b.Property<long>("UserId")
                        .HasColumnType("bigint");

                    b.HasKey("LoginProvider", "ProviderKey");

                    b.HasIndex("UserId");

                    b.ToTable("AspNetUserLogins", (string)null);
                });

            modelBuilder.Entity("Microsoft.AspNetCore.Identity.IdentityUserRole<long>", b =>
                {
                    b.Property<long>("UserId")
                        .HasColumnType("bigint");

                    b.Property<long>("RoleId")
                        .HasColumnType("bigint");

                    b.HasKey("UserId", "RoleId");

                    b.HasIndex("RoleId");

                    b.ToTable("AspNetUserRoles", (string)null);
                });

            modelBuilder.Entity("Microsoft.AspNetCore.Identity.IdentityUserToken<long>", b =>
                {
                    b.Property<long>("UserId")
                        .HasColumnType("bigint");

                    b.Property<string>("LoginProvider")
                        .HasColumnType("text");

                    b.Property<string>("Name")
                        .HasColumnType("text");

                    b.Property<string>("Value")
                        .HasColumnType("text");

                    b.HasKey("UserId", "LoginProvider", "Name");

                    b.ToTable("AspNetUserTokens", (string)null);
                });

            modelBuilder.Entity("CMS.WebApi.Models.Entities.ApiToken", b =>
                {
                    b.HasOne("CMS.WebApi.Models.Entities.User", "User")
                        .WithMany()
                        .HasForeignKey("UserId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("User");
                });

            modelBuilder.Entity("CMS.WebApi.Models.Entities.Category", b =>
                {
                    b.HasOne("CMS.WebApi.Models.Entities.Client", "Client")
                        .WithMany()
                        .HasForeignKey("ClientId")
                        .OnDelete(DeleteBehavior.Cascade);

                    b.HasOne("CMS.WebApi.Models.Entities.Category", "ParentCategory")
                        .WithMany("ChildCategories")
                        .HasForeignKey("ParentCategoryId")
                        .OnDelete(DeleteBehavior.SetNull);

                    b.Navigation("Client");

                    b.Navigation("ParentCategory");
                });

            modelBuilder.Entity("CMS.WebApi.Models.Entities.CollectionComponent", b =>
                {
                    b.HasOne("CMS.WebApi.Models.Entities.CollectionListing", "Collection")
                        .WithMany("Components")
                        .HasForeignKey("CollectionId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("CMS.WebApi.Models.Entities.ComponentListing", "Component")
                        .WithMany("CollectionComponents")
                        .HasForeignKey("ComponentId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("Collection");

                    b.Navigation("Component");
                });

            modelBuilder.Entity("CMS.WebApi.Models.Entities.CollectionField", b =>
                {
                    b.HasOne("CMS.WebApi.Models.Entities.CollectionListing", "Collection")
                        .WithMany("Fields")
                        .HasForeignKey("CollectionId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("CMS.WebApi.Models.Entities.CollectionField", "DependentOn")
                        .WithMany()
                        .HasForeignKey("DependentOnId")
                        .OnDelete(DeleteBehavior.SetNull);

                    b.HasOne("CMS.WebApi.Models.Entities.FieldType", "FieldType")
                        .WithMany()
                        .HasForeignKey("FieldTypeId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("Collection");

                    b.Navigation("DependentOn");

                    b.Navigation("FieldType");
                });

            modelBuilder.Entity("CMS.WebApi.Models.Entities.CollectionFieldConfig", b =>
                {
                    b.HasOne("CMS.WebApi.Models.Entities.CollectionField", "CollectionField")
                        .WithMany("Configs")
                        .HasForeignKey("CollectionFieldId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("CMS.WebApi.Models.Entities.FieldConfig", "FieldConfig")
                        .WithMany()
                        .HasForeignKey("FieldConfigId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("CollectionField");

                    b.Navigation("FieldConfig");
                });

            modelBuilder.Entity("CMS.WebApi.Models.Entities.CollectionListing", b =>
                {
                    b.HasOne("CMS.WebApi.Models.Entities.Category", "Category")
                        .WithMany("Collections")
                        .HasForeignKey("CategoryId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("Category");
                });

            modelBuilder.Entity("CMS.WebApi.Models.Entities.ComponentComponent", b =>
                {
                    b.HasOne("CMS.WebApi.Models.Entities.ComponentListing", "ChildComponent")
                        .WithMany("ParentComponents")
                        .HasForeignKey("ChildComponentId")
                        .OnDelete(DeleteBehavior.Restrict)
                        .IsRequired();

                    b.HasOne("CMS.WebApi.Models.Entities.ComponentListing", "ParentComponent")
                        .WithMany("ChildComponents")
                        .HasForeignKey("ParentComponentId")
                        .OnDelete(DeleteBehavior.Restrict)
                        .IsRequired();

                    b.Navigation("ChildComponent");

                    b.Navigation("ParentComponent");
                });

            modelBuilder.Entity("CMS.WebApi.Models.Entities.ComponentField", b =>
                {
                    b.HasOne("CMS.WebApi.Models.Entities.ComponentListing", "Component")
                        .WithMany("Fields")
                        .HasForeignKey("ComponentId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("CMS.WebApi.Models.Entities.ComponentField", "DependentOn")
                        .WithMany()
                        .HasForeignKey("DependentOnId")
                        .OnDelete(DeleteBehavior.SetNull);

                    b.HasOne("CMS.WebApi.Models.Entities.FieldType", "FieldType")
                        .WithMany()
                        .HasForeignKey("FieldTypeId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("Component");

                    b.Navigation("DependentOn");

                    b.Navigation("FieldType");
                });

            modelBuilder.Entity("CMS.WebApi.Models.Entities.ComponentFieldConfig", b =>
                {
                    b.HasOne("CMS.WebApi.Models.Entities.ComponentField", "ComponentField")
                        .WithMany("Configs")
                        .HasForeignKey("ComponentFieldId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("CMS.WebApi.Models.Entities.FieldConfig", "FieldConfig")
                        .WithMany()
                        .HasForeignKey("FieldConfigId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("ComponentField");

                    b.Navigation("FieldConfig");
                });

            modelBuilder.Entity("CMS.WebApi.Models.Entities.ComponentListing", b =>
                {
                    b.HasOne("CMS.WebApi.Models.Entities.Category", null)
                        .WithMany("Components")
                        .HasForeignKey("CategoryId");
                });

            modelBuilder.Entity("CMS.WebApi.Models.Entities.ContentEntry", b =>
                {
                    b.HasOne("CMS.WebApi.Models.Entities.CollectionListing", "Collection")
                        .WithMany("ContentEntries")
                        .HasForeignKey("CollectionId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("Collection");
                });

            modelBuilder.Entity("CMS.WebApi.Models.Entities.FieldConfig", b =>
                {
                    b.HasOne("CMS.WebApi.Models.Entities.ConfigType", "ConfigType")
                        .WithMany("FieldConfigs")
                        .HasForeignKey("ConfigTypeId")
                        .OnDelete(DeleteBehavior.SetNull);

                    b.HasOne("CMS.WebApi.Models.Entities.FieldType", "FieldType")
                        .WithMany("FieldConfigs")
                        .HasForeignKey("FieldTypeId")
                        .OnDelete(DeleteBehavior.SetNull);

                    b.Navigation("ConfigType");

                    b.Navigation("FieldType");
                });

            modelBuilder.Entity("CMS.WebApi.Models.Entities.Media", b =>
                {
                    b.HasOne("CMS.WebApi.Models.Entities.MediaFolder", "Folder")
                        .WithMany("MediaFiles")
                        .HasForeignKey("FolderId")
                        .OnDelete(DeleteBehavior.SetNull);

                    b.HasOne("CMS.WebApi.Models.Entities.MediaFolder", null)
                        .WithMany("Media")
                        .HasForeignKey("MediaFolderId");

                    b.HasOne("CMS.WebApi.Models.Entities.User", "User")
                        .WithMany()
                        .HasForeignKey("UserId")
                        .OnDelete(DeleteBehavior.SetNull);

                    b.Navigation("Folder");

                    b.Navigation("User");
                });

            modelBuilder.Entity("CMS.WebApi.Models.Entities.MediaFolder", b =>
                {
                    b.HasOne("CMS.WebApi.Models.Entities.MediaFolder", "Parent")
                        .WithMany("Children")
                        .HasForeignKey("ParentId")
                        .OnDelete(DeleteBehavior.SetNull);

                    b.HasOne("CMS.WebApi.Models.Entities.User", "User")
                        .WithMany()
                        .HasForeignKey("UserId")
                        .OnDelete(DeleteBehavior.SetNull);

                    b.Navigation("Parent");

                    b.Navigation("User");
                });

            modelBuilder.Entity("Microsoft.AspNetCore.Identity.IdentityRoleClaim<long>", b =>
                {
                    b.HasOne("Microsoft.AspNetCore.Identity.IdentityRole<long>", null)
                        .WithMany()
                        .HasForeignKey("RoleId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();
                });

            modelBuilder.Entity("Microsoft.AspNetCore.Identity.IdentityUserClaim<long>", b =>
                {
                    b.HasOne("CMS.WebApi.Models.Entities.User", null)
                        .WithMany()
                        .HasForeignKey("UserId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();
                });

            modelBuilder.Entity("Microsoft.AspNetCore.Identity.IdentityUserLogin<long>", b =>
                {
                    b.HasOne("CMS.WebApi.Models.Entities.User", null)
                        .WithMany()
                        .HasForeignKey("UserId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();
                });

            modelBuilder.Entity("Microsoft.AspNetCore.Identity.IdentityUserRole<long>", b =>
                {
                    b.HasOne("Microsoft.AspNetCore.Identity.IdentityRole<long>", null)
                        .WithMany()
                        .HasForeignKey("RoleId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("CMS.WebApi.Models.Entities.User", null)
                        .WithMany()
                        .HasForeignKey("UserId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();
                });

            modelBuilder.Entity("Microsoft.AspNetCore.Identity.IdentityUserToken<long>", b =>
                {
                    b.HasOne("CMS.WebApi.Models.Entities.User", null)
                        .WithMany()
                        .HasForeignKey("UserId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();
                });

            modelBuilder.Entity("CMS.WebApi.Models.Entities.Category", b =>
                {
                    b.Navigation("ChildCategories");

                    b.Navigation("Collections");

                    b.Navigation("Components");
                });

            modelBuilder.Entity("CMS.WebApi.Models.Entities.CollectionField", b =>
                {
                    b.Navigation("Configs");
                });

            modelBuilder.Entity("CMS.WebApi.Models.Entities.CollectionListing", b =>
                {
                    b.Navigation("Components");

                    b.Navigation("ContentEntries");

                    b.Navigation("Fields");
                });

            modelBuilder.Entity("CMS.WebApi.Models.Entities.ComponentField", b =>
                {
                    b.Navigation("Configs");
                });

            modelBuilder.Entity("CMS.WebApi.Models.Entities.ComponentListing", b =>
                {
                    b.Navigation("ChildComponents");

                    b.Navigation("CollectionComponents");

                    b.Navigation("Fields");

                    b.Navigation("ParentComponents");
                });

            modelBuilder.Entity("CMS.WebApi.Models.Entities.ConfigType", b =>
                {
                    b.Navigation("FieldConfigs");
                });

            modelBuilder.Entity("CMS.WebApi.Models.Entities.FieldType", b =>
                {
                    b.Navigation("FieldConfigs");
                });

            modelBuilder.Entity("CMS.WebApi.Models.Entities.MediaFolder", b =>
                {
                    b.Navigation("Children");

                    b.Navigation("Media");

                    b.Navigation("MediaFiles");
                });
#pragma warning restore 612, 618
        }
    }
}
