using System;
using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

#pragma warning disable CA1814 // Prefer jagged arrays over multidimensional

namespace CMS.WebApi.Migrations
{
    /// <inheritdoc />
    public partial class SeedInitialData : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.InsertData(
                table: "config_types",
                columns: new[] { "id", "additional_info", "config_type_desc", "config_type_name", "CreatedAt", "CreatedBy", "disclaimer_text", "display_name", "is_active", "ModifiedAt", "ModifiedBy", "placeholder_text" },
                values: new object[,]
                {
                    { 1, null, "Basic properties for fields", "properties", new DateTime(2025, 7, 22, 5, 47, 0, 650, DateTimeKind.Utc).AddTicks(3678), "System", "Basic properties for fields", "Properties", true, null, null, "Properties" },
                    { 2, null, "UI attributes for fields", "attributes", new DateTime(2025, 7, 22, 5, 47, 0, 650, DateTimeKind.Utc).AddTicks(3683), "System", "Attributes for fields", "Attributes", true, null, null, "Attributes" },
                    { 3, null, "Validation rules for fields", "validations", new DateTime(2025, 7, 22, 5, 47, 0, 650, DateTimeKind.Utc).AddTicks(3685), "System", "Validation rules for fields", "Validations", true, null, null, "Validations" }
                });

            migrationBuilder.InsertData(
                table: "field_types",
                columns: new[] { "id", "CreatedAt", "CreatedBy", "display_name", "field_type_desc", "field_type_name", "help_text", "is_active", "logo_image_path", "ModifiedAt", "ModifiedBy" },
               values: new object[,]
{
    { 1, new DateTime(2025, 7, 22, 5, 47, 0, 650, DateTimeKind.Utc).AddTicks(5000), "System", "Text", "Simple text field", "text", "Enter text here", true, null, null, null },
    { 2, new DateTime(2025, 7, 22, 5, 47, 0, 650, DateTimeKind.Utc).AddTicks(5002), "System", "Number", "Numeric field", "number", "Enter a number", true, null, null, null },
    { 3, new DateTime(2025, 7, 22, 5, 47, 0, 650, DateTimeKind.Utc).AddTicks(5004), "System", "Date", "Date field", "date", "Select a date", true, null, null, null },
    { 4, new DateTime(2025, 7, 22, 5, 47, 0, 650, DateTimeKind.Utc).AddTicks(5006), "System", "Image", "Image upload field", "image", "Upload an image", true, null, null, null },
    { 5, new DateTime(2025, 7, 22, 5, 47, 0, 650, DateTimeKind.Utc).AddTicks(5008), "System", "Rich Text", "Rich text editor", "rich_text", "Enter formatted text", true, null, null, null },
    { 6, new DateTime(2025, 7, 22, 5, 47, 0, 650, DateTimeKind.Utc).AddTicks(5010), "System", "Masked", "Masked input field", "mask", "Enter masked value (e.g., SSN, EIN)", true, null, null, null },
    { 7, new DateTime(2025, 7, 22, 5, 47, 0, 650, DateTimeKind.Utc).AddTicks(5011), "System", "Time", "Time input field", "time", "Select a time", true, null, null, null },
    { 8, new DateTime(2025, 7, 22, 5, 47, 0, 650, DateTimeKind.Utc).AddTicks(5012), "System", "Editor", "Rich text editor", "editor", "Enter formatted text", true, null, null, null },
    { 9, new DateTime(2025, 7, 22, 5, 47, 0, 650, DateTimeKind.Utc).AddTicks(5014), "System", "Password", "Password input field", "password", "Enter password", true, null, null, null },
    { 10, new DateTime(2025, 7, 22, 5, 47, 0, 650, DateTimeKind.Utc).AddTicks(5016), "System", "Autocomplete", "Autocomplete suggestions", "autocomplete", "Start typing for suggestions", true, null, null, null },
    { 11, new DateTime(2025, 7, 22, 5, 47, 0, 650, DateTimeKind.Utc).AddTicks(5018), "System", "Cascade Select", "Cascade selection field", "cascade_select", "Select dependent options", true, null, null, null },
    { 12, new DateTime(2025, 7, 22, 5, 47, 0, 650, DateTimeKind.Utc).AddTicks(5020), "System", "Dropdown", "Dropdown selection", "dropdown", "Select from dropdown", true, null, null, null },
    { 13, new DateTime(2025, 7, 22, 5, 47, 0, 650, DateTimeKind.Utc).AddTicks(5022), "System", "File", "File upload field", "file", "Upload a file", true, null, null, null },
    { 14, new DateTime(2025, 7, 22, 5, 47, 0, 650, DateTimeKind.Utc).AddTicks(5024), "System", "Multi-State Checkbox", "Multi-state checkbox", "multi_state_checkbox", "Select multiple states", true, null, null, null },
    { 15, new DateTime(2025, 7, 22, 5, 47, 0, 650, DateTimeKind.Utc).AddTicks(5026), "System", "Multi-Select", "Multi-select field", "multi_select", "Select multiple options", true, null, null, null },
    { 16, new DateTime(2025, 7, 22, 5, 47, 0, 650, DateTimeKind.Utc).AddTicks(5028), "System", "Mention", "Mention users or tags", "mention", "Mention users or tags", true, null, null, null },
    { 17, new DateTime(2025, 7, 22, 5, 47, 0, 650, DateTimeKind.Utc).AddTicks(5030), "System", "Textarea", "Multi-line text input", "textarea", "Enter multi-line text", true, null, null, null },
    { 18, new DateTime(2025, 7, 22, 5, 47, 0, 650, DateTimeKind.Utc).AddTicks(5032), "System", "OTP", "One-time password input", "otp", "Enter one-time password", true, null, null, null },
    { 19, new DateTime(2025, 7, 22, 5, 47, 0, 650, DateTimeKind.Utc).AddTicks(5034), "System", "Checkbox", "Checkbox input field", "checkbox", "Select one or more options", true, null, null, null },
    { 20, new DateTime(2025, 7, 22, 5, 47, 0, 650, DateTimeKind.Utc).AddTicks(5036), "System", "RadioButton", "Radio button selection field", "radio_button", "Select one option", true, null, null, null },
    { 21, new DateTime(2025, 7, 22, 5, 47, 0, 650, DateTimeKind.Utc).AddTicks(5038), "System", "InputSwitch", "Toggle switch input", "input_switch", "Toggle the option on or off", true, null, null, null },
    { 22, new DateTime(2025, 7, 22, 5, 47, 0, 650, DateTimeKind.Utc).AddTicks(5040), "System", "Dummy", "Dummy field", "dummy", "Dummy Field", true, null, null, null },
    { 23, new DateTime(2025, 7, 22, 5, 47, 0, 650, DateTimeKind.Utc).AddTicks(5042), "System", "Api details", "API details configuration", "api_details", "Configure API details", true, null, null, null },
    { 24, new DateTime(2025, 7, 22, 5, 47, 0, 650, DateTimeKind.Utc).AddTicks(5044), "System", "Enumeration", "Enumeration field for fixed value lists", "enumeration", "Define a list of fixed values", true, null, null, null },
    { 26, new DateTime(2025, 7, 22, 5, 47, 0, 650, DateTimeKind.Utc).AddTicks(5046), "System", "Search", "Search input field", "search", "Enter search terms", true, null, null, null },
    { 27, new DateTime(2025, 7, 22, 5, 47, 0, 650, DateTimeKind.Utc).AddTicks(5048), "System", "Select", "Select dropdown field", "select", "Select an option", true, null, null, null }

                });

            migrationBuilder.InsertData(
                table: "field_configs",
                columns: new[] { "id", "config_name", "config_type_id", "CreatedAt", "CreatedBy", "field_type_id", "is_active", "ModifiedAt", "ModifiedBy", "value_type" },
                values: new object[,]
                {
                    { 1, "dependOn", 1, new DateTime(2025, 7, 22, 5, 47, 0, 650, DateTimeKind.Utc).AddTicks(4158), "System", 1, true, null, null, "text" },
                    { 2, "unique-id", 1, new DateTime(2025, 7, 22, 5, 47, 0, 650, DateTimeKind.Utc).AddTicks(4160), "System", 1, true, null, null, "text" },
                    { 3, "display-name", 1, new DateTime(2025, 7, 22, 5, 47, 0, 650, DateTimeKind.Utc).AddTicks(4162), "System", 1, true, null, null, "text" },
                    { 4, "name", 1, new DateTime(2025, 7, 22, 5, 47, 0, 650, DateTimeKind.Utc).AddTicks(4164), "System", 1, true, null, null, "text" },
                    { 5, "description", 1, new DateTime(2025, 7, 22, 5, 47, 0, 650, DateTimeKind.Utc).AddTicks(4166), "System", 1, true, null, null, "text" },
                    { 6, "is-visible", 1, new DateTime(2025, 7, 22, 5, 47, 0, 650, DateTimeKind.Utc).AddTicks(4168), "System", 1, true, null, null, "boolean" },
                    { 7, "keyfilter", 2, new DateTime(2025, 7, 22, 5, 47, 0, 650, DateTimeKind.Utc).AddTicks(4170), "System", 1, true, null, null, "regex" },
                    { 8, "placeholder", 2, new DateTime(2025, 7, 22, 5, 47, 0, 650, DateTimeKind.Utc).AddTicks(4172), "System", 1, true, null, null, "text" },
                    { 9, "helpText", 2, new DateTime(2025, 7, 22, 5, 47, 0, 650, DateTimeKind.Utc).AddTicks(4174), "System", 1, true, null, null, "text" },
                    { 10, "FloatLable", 2, new DateTime(2025, 7, 22, 5, 47, 0, 650, DateTimeKind.Utc).AddTicks(4176), "System", 1, true, null, null, "text" },
                    { 11, "variant", 2, new DateTime(2025, 7, 22, 5, 47, 0, 650, DateTimeKind.Utc).AddTicks(4178), "System", 1, true, null, null, "options" },
                    { 12, "invalid", 2, new DateTime(2025, 7, 22, 5, 47, 0, 650, DateTimeKind.Utc).AddTicks(4180), "System", 1, true, null, null, "boolean" },
                    { 13, "disabled", 2, new DateTime(2025, 7, 22, 5, 47, 0, 650, DateTimeKind.Utc).AddTicks(4182), "System", 1, true, null, null, "boolean" },
                    { 14, "icon", 2, new DateTime(2025, 7, 22, 5, 47, 0, 650, DateTimeKind.Utc).AddTicks(4184), "System", 1, true, null, null, "icon" },
                    { 15, "tooltip", 2, new DateTime(2025, 7, 22, 5, 47, 0, 650, DateTimeKind.Utc).AddTicks(4186), "System", 1, true, null, null, "text" },
                    { 16, "tooltipOptions", 2, new DateTime(2025, 7, 22, 5, 47, 0, 650, DateTimeKind.Utc).AddTicks(4188), "System", 1, true, null, null, "text" },
                    { 17, "autoClear", 2, new DateTime(2025, 7, 22, 5, 47, 0, 650, DateTimeKind.Utc).AddTicks(4190), "System", 1, true, null, null, "boolean" },
                    { 18, "required", 3, new DateTime(2025, 7, 22, 5, 47, 0, 650, DateTimeKind.Utc).AddTicks(4192), "System", 1, true, null, null, "boolean" },
                    { 19, "minLength", 2, new DateTime(2025, 7, 22, 5, 47, 0, 650, DateTimeKind.Utc).AddTicks(4194), "System", 1, true, null, null, "number" },
                    { 20, "maxLength", 2, new DateTime(2025, 7, 22, 5, 47, 0, 650, DateTimeKind.Utc).AddTicks(4196), "System", 1, true, null, null, "number" },
                    { 21, "regex", 3, new DateTime(2025, 7, 22, 5, 47, 0, 650, DateTimeKind.Utc).AddTicks(4198), "System", 1, true, null, null, "regex" },
                    { 22, "api-url", 1, new DateTime(2025, 7, 22, 5, 47, 0, 650, DateTimeKind.Utc).AddTicks(4200), "System", 1, true, null, null, "url" },
                    { 23, "dependOn", 1, new DateTime(2025, 7, 22, 5, 47, 0, 650, DateTimeKind.Utc).AddTicks(4202), "System", 2, true, null, null, "text" },
                    { 24, "unique-id", 1, new DateTime(2025, 7, 22, 5, 47, 0, 650, DateTimeKind.Utc).AddTicks(4204), "System", 2, true, null, null, "text" },
                    { 25, "display-name", 1, new DateTime(2025, 7, 22, 5, 47, 0, 650, DateTimeKind.Utc).AddTicks(4206), "System", 2, true, null, null, "text" },
                    { 26, "name", 1, new DateTime(2025, 7, 22, 5, 47, 0, 650, DateTimeKind.Utc).AddTicks(4208), "System", 2, true, null, null, "text" },
                    { 27, "description", 1, new DateTime(2025, 7, 22, 5, 47, 0, 650, DateTimeKind.Utc).AddTicks(4210), "System", 2, true, null, null, "text" },
                    { 28, "is-visible", 1, new DateTime(2025, 7, 22, 5, 47, 0, 650, DateTimeKind.Utc).AddTicks(4212), "System", 2, true, null, null, "boolean" },
                    { 29, "placeholder", 2, new DateTime(2025, 7, 22, 5, 47, 0, 650, DateTimeKind.Utc).AddTicks(4214), "System", 2, true, null, null, "text" },
                    { 30, "helpText", 2, new DateTime(2025, 7, 22, 5, 47, 0, 650, DateTimeKind.Utc).AddTicks(4216), "System", 2, true, null, null, "text" },
                    { 31, "FloatLable", 2, new DateTime(2025, 7, 22, 5, 47, 0, 650, DateTimeKind.Utc).AddTicks(4218), "System", 2, true, null, null, "text" },
                    { 32, "variant", 2, new DateTime(2025, 7, 22, 5, 47, 0, 650, DateTimeKind.Utc).AddTicks(4220), "System", 2, true, null, null, "options" },
                    { 33, "invalid", 2, new DateTime(2025, 7, 22, 5, 47, 0, 650, DateTimeKind.Utc).AddTicks(4222), "System", 2, true, null, null, "boolean" },
                    { 34, "disabled", 2, new DateTime(2025, 7, 22, 5, 47, 0, 650, DateTimeKind.Utc).AddTicks(4224), "System", 2, true, null, null, "boolean" },
                    { 35, "useGrouping", 2, new DateTime(2025, 7, 22, 5, 47, 0, 650, DateTimeKind.Utc).AddTicks(4226), "System", 2, true, null, null, "boolean" },
                    { 36, "locale", 2, new DateTime(2025, 7, 22, 5, 47, 0, 650, DateTimeKind.Utc).AddTicks(4228), "System", 2, true, null, null, "text" },
                    { 37, "suffix", 2, new DateTime(2025, 7, 22, 5, 47, 0, 650, DateTimeKind.Utc).AddTicks(4230), "System", 2, true, null, null, "text" },
                    { 38, "prefix", 2, new DateTime(2025, 7, 22, 5, 47, 0, 650, DateTimeKind.Utc).AddTicks(4232), "System", 2, true, null, null, "text" },
                    { 39, "showButtons", 2, new DateTime(2025, 7, 22, 5, 47, 0, 650, DateTimeKind.Utc).AddTicks(4234), "System", 2, true, null, null, "boolean" },
                    { 40, "mode", 2, new DateTime(2025, 7, 22, 5, 47, 0, 650, DateTimeKind.Utc).AddTicks(4236), "System", 2, true, null, null, "options" },
                    { 41, "currency", 2, new DateTime(2025, 7, 22, 5, 47, 0, 650, DateTimeKind.Utc).AddTicks(4238), "System", 2, true, null, null, "options" },
                    { 42, "step", 2, new DateTime(2025, 7, 22, 5, 47, 0, 650, DateTimeKind.Utc).AddTicks(4240), "System", 2, true, null, null, "number" },
                    { 43, "min", 2, new DateTime(2025, 7, 22, 5, 47, 0, 650, DateTimeKind.Utc).AddTicks(4242), "System", 2, true, null, null, "number" },
                    { 44, "max", 2, new DateTime(2025, 7, 22, 5, 47, 0, 650, DateTimeKind.Utc).AddTicks(4244), "System", 2, true, null, null, "number" },
                    { 45, "minFractionDigits", 2, new DateTime(2025, 7, 22, 5, 47, 0, 650, DateTimeKind.Utc).AddTicks(4246), "System", 2, true, null, null, "number" },
                    { 46, "maxFractionDigits", 2, new DateTime(2025, 7, 22, 5, 47, 0, 650, DateTimeKind.Utc).AddTicks(4248), "System", 2, true, null, null, "number" },
                    { 47, "incrementButtonIcon", 2, new DateTime(2025, 7, 22, 5, 47, 0, 650, DateTimeKind.Utc).AddTicks(4250), "System", 2, true, null, null, "icon" },
                    { 48, "decrementButtonIcon", 2, new DateTime(2025, 7, 22, 5, 47, 0, 650, DateTimeKind.Utc).AddTicks(4252), "System", 2, true, null, null, "icon" },
                    { 49, "decrementButtonClassName", 2, new DateTime(2025, 7, 22, 5, 47, 0, 650, DateTimeKind.Utc).AddTicks(4254), "System", 2, true, null, null, "text" },
                    { 50, "incrementButtonClassName", 2, new DateTime(2025, 7, 22, 5, 47, 0, 650, DateTimeKind.Utc).AddTicks(4256), "System", 2, true, null, null, "text" },
                    { 51, "buttonLayout", 2, new DateTime(2025, 7, 22, 5, 47, 0, 650, DateTimeKind.Utc).AddTicks(4258), "System", 2, true, null, null, "text" },
                    { 52, "autoClear", 2, new DateTime(2025, 7, 22, 5, 47, 0, 650, DateTimeKind.Utc).AddTicks(4260), "System", 2, true, null, null, "boolean" },
                    { 53, "icon", 2, new DateTime(2025, 7, 22, 5, 47, 0, 650, DateTimeKind.Utc).AddTicks(4262), "System", 2, true, null, null, "icon" },
                    { 54, "required", 3, new DateTime(2025, 7, 22, 5, 47, 0, 650, DateTimeKind.Utc).AddTicks(4264), "System", 2, true, null, null, "boolean" },
                    { 55, "minValue", 3, new DateTime(2025, 7, 22, 5, 47, 0, 650, DateTimeKind.Utc).AddTicks(4266), "System", 2, true, null, null, "number" },
                    { 56, "maxValue", 3, new DateTime(2025, 7, 22, 5, 47, 0, 650, DateTimeKind.Utc).AddTicks(4268), "System", 2, true, null, null, "number" },
                    { 57, "minFractionDigits", 3, new DateTime(2025, 7, 22, 5, 47, 0, 650, DateTimeKind.Utc).AddTicks(4270), "System", 2, true, null, null, "number" },
                    { 58, "maxFractionDigits", 3, new DateTime(2025, 7, 22, 5, 47, 0, 650, DateTimeKind.Utc).AddTicks(4272), "System", 2, true, null, null, "number" },
                    { 59, "regex", 3, new DateTime(2025, 7, 22, 5, 47, 0, 650, DateTimeKind.Utc).AddTicks(4274), "System", 2, true, null, null, "regex" },
                    { 60, "dependOn", 1, new DateTime(2025, 7, 22, 5, 47, 0, 650, DateTimeKind.Utc).AddTicks(4276), "System", 6, true, null, null, "text" },
                    { 61, "required", 3, new DateTime(2025, 7, 22, 5, 47, 0, 650, DateTimeKind.Utc).AddTicks(4278), "System", 6, true, null, null, "boolean" },
                    { 62, "placeholder", 2, new DateTime(2025, 7, 22, 5, 47, 0, 650, DateTimeKind.Utc).AddTicks(4280), "System", 6, true, null, null, "text" },
                    { 63, "mask", 2, new DateTime(2025, 7, 22, 5, 47, 0, 650, DateTimeKind.Utc).AddTicks(4282), "System", 6, true, null, null, "regex" },
                    { 64, "helpText", 2, new DateTime(2025, 7, 22, 5, 47, 0, 650, DateTimeKind.Utc).AddTicks(4284), "System", 6, true, null, null, "text" },
                    { 65, "regex", 3, new DateTime(2025, 7, 22, 5, 47, 0, 650, DateTimeKind.Utc).AddTicks(4286), "System", 6, true, null, null, "regex" },
                    { 66, "FloatLable", 2, new DateTime(2025, 7, 22, 5, 47, 0, 650, DateTimeKind.Utc).AddTicks(4288), "System", 6, true, null, null, "text" },
                    { 67, "unmask", 3, new DateTime(2025, 7, 22, 5, 47, 0, 650, DateTimeKind.Utc).AddTicks(4290), "System", 6, true, null, null, "boolean" },
                    { 68, "variant", 2, new DateTime(2025, 7, 22, 5, 47, 0, 650, DateTimeKind.Utc).AddTicks(4292), "System", 6, true, null, null, "options" },
                    { 69, "invalid", 2, new DateTime(2025, 7, 22, 5, 47, 0, 650, DateTimeKind.Utc).AddTicks(4294), "System", 6, true, null, null, "boolean" },
                    { 70, "disabled", 2, new DateTime(2025, 7, 22, 5, 47, 0, 650, DateTimeKind.Utc).AddTicks(4296), "System", 6, true, null, null, "boolean" },
                    { 71, "slotChar", 3, new DateTime(2025, 7, 22, 5, 47, 0, 650, DateTimeKind.Utc).AddTicks(4298), "System", 6, true, null, null, "regex" },
                    { 72, "autoClear", 2, new DateTime(2025, 7, 22, 5, 47, 0, 650, DateTimeKind.Utc).AddTicks(4300), "System", 6, true, null, null, "boolean" },
                    { 73, "icon", 2, new DateTime(2025, 7, 22, 5, 47, 0, 650, DateTimeKind.Utc).AddTicks(4302), "System", 6, true, null, null, "icon" },
                    { 74, "dependOn", 1, new DateTime(2025, 7, 22, 5, 47, 0, 650, DateTimeKind.Utc).AddTicks(4304), "System", 3, true, null, null, "text" },
                    { 75, "dateFormat", 2, new DateTime(2025, 7, 22, 5, 47, 0, 650, DateTimeKind.Utc).AddTicks(4306), "System", 3, true, null, null, "date" },
                    { 76, "locale", 2, new DateTime(2025, 7, 22, 5, 47, 0, 650, DateTimeKind.Utc).AddTicks(4308), "System", 3, true, null, null, "text" },
                    { 77, "showIcon", 2, new DateTime(2025, 7, 22, 5, 47, 0, 650, DateTimeKind.Utc).AddTicks(4310), "System", 3, true, null, null, "boolean" },
                    { 78, "minDate", 2, new DateTime(2025, 7, 22, 5, 47, 0, 650, DateTimeKind.Utc).AddTicks(4312), "System", 3, true, null, null, "date" },
                    { 79, "maxDate", 2, new DateTime(2025, 7, 22, 5, 47, 0, 650, DateTimeKind.Utc).AddTicks(4314), "System", 3, true, null, null, "date" },
                    { 80, "readOnlyInput", 2, new DateTime(2025, 7, 22, 5, 47, 0, 650, DateTimeKind.Utc).AddTicks(4316), "System", 3, true, null, null, "boolean" },
                    { 81, "selectionMode", 2, new DateTime(2025, 7, 22, 5, 47, 0, 650, DateTimeKind.Utc).AddTicks(4318), "System", 3, true, null, null, "options" },
                    { 82, "hideOnRangeSelection", 2, new DateTime(2025, 7, 22, 5, 47, 0, 650, DateTimeKind.Utc).AddTicks(4320), "System", 3, true, null, null, "boolean" },
                    { 83, "showButtonBar", 2, new DateTime(2025, 7, 22, 5, 47, 0, 650, DateTimeKind.Utc).AddTicks(4322), "System", 3, true, null, null, "boolean" },
                    { 84, "showTime", 2, new DateTime(2025, 7, 22, 5, 47, 0, 650, DateTimeKind.Utc).AddTicks(4324), "System", 3, true, null, null, "date" },
                    { 85, "hourFormat", 2, new DateTime(2025, 7, 22, 5, 47, 0, 650, DateTimeKind.Utc).AddTicks(4326), "System", 3, true, null, null, "options" },
                    { 86, "view", 2, new DateTime(2025, 7, 22, 5, 47, 0, 650, DateTimeKind.Utc).AddTicks(4328), "System", 3, true, null, null, "options" },
                    { 87, "numberOfMonths", 2, new DateTime(2025, 7, 22, 5, 47, 0, 650, DateTimeKind.Utc).AddTicks(4330), "System", 3, true, null, null, "number" },
                    { 88, "FloatLable", 2, new DateTime(2025, 7, 22, 5, 47, 0, 650, DateTimeKind.Utc).AddTicks(4332), "System", 3, true, null, null, "text" },
                    { 89, "variant", 2, new DateTime(2025, 7, 22, 5, 47, 0, 650, DateTimeKind.Utc).AddTicks(4334), "System", 3, true, null, null, "options" },
                    { 90, "invalid", 2, new DateTime(2025, 7, 22, 5, 47, 0, 650, DateTimeKind.Utc).AddTicks(4336), "System", 3, true, null, null, "boolean" },
                    { 91, "disabled", 2, new DateTime(2025, 7, 22, 5, 47, 0, 650, DateTimeKind.Utc).AddTicks(4338), "System", 3, true, null, null, "boolean" },
                    { 92, "timeOnly", 2, new DateTime(2025, 7, 22, 5, 47, 0, 650, DateTimeKind.Utc).AddTicks(4340), "System", 3, true, null, null, "date" },
                    { 93, "icon", 2, new DateTime(2025, 7, 22, 5, 47, 0, 650, DateTimeKind.Utc).AddTicks(4342), "System", 3, true, null, null, "icon" },
                    { 94, "inline", 2, new DateTime(2025, 7, 22, 5, 47, 0, 650, DateTimeKind.Utc).AddTicks(4344), "System", 3, true, null, null, "boolean" },
                    { 95, "showWeek", 2, new DateTime(2025, 7, 22, 5, 47, 0, 650, DateTimeKind.Utc).AddTicks(4346), "System", 3, true, null, null, "boolean" },
                    { 96, "readonly", 2, new DateTime(2025, 7, 22, 5, 47, 0, 650, DateTimeKind.Utc).AddTicks(4348), "System", 3, true, null, null, "boolean" },
                    { 97, "dependon", 1, new DateTime(2025, 7, 22, 5, 47, 0, 650, DateTimeKind.Utc).AddTicks(4350), "System", 9, true, null, null, "text" },
                    { 98, "feedback_(true/false)", 2, new DateTime(2025, 7, 22, 5, 47, 0, 650, DateTimeKind.Utc).AddTicks(4352), "System", 9, true, null, null, "boolean" },
                    { 99, "required", 3, new DateTime(2025, 7, 22, 5, 47, 0, 650, DateTimeKind.Utc).AddTicks(4354), "System", 9, true, null, null, "boolean" },
                    { 100, "unique-id", 1, new DateTime(2025, 7, 22, 5, 47, 0, 650, DateTimeKind.Utc).AddTicks(4356), "System", 9, true, null, null, "text" },
                    { 101, "promptlabel", 2, new DateTime(2025, 7, 22, 5, 47, 0, 650, DateTimeKind.Utc).AddTicks(4358), "System", 9, true, null, null, "text" },
                    { 102, "minlength", 2, new DateTime(2025, 7, 22, 5, 47, 0, 650, DateTimeKind.Utc).AddTicks(4360), "System", 9, true, null, null, "number" },
                    { 103, "display-name", 1, new DateTime(2025, 7, 22, 5, 47, 0, 650, DateTimeKind.Utc).AddTicks(4362), "System", 9, true, null, null, "text" },
                    { 104, "weaklabel", 2, new DateTime(2025, 7, 22, 5, 47, 0, 650, DateTimeKind.Utc).AddTicks(4364), "System", 9, true, null, null, "text" },
                    { 105, "maxlength", 2, new DateTime(2025, 7, 22, 5, 47, 0, 650, DateTimeKind.Utc).AddTicks(4366), "System", 9, true, null, null, "number" },
                    { 106, "name", 1, new DateTime(2025, 7, 22, 5, 47, 0, 650, DateTimeKind.Utc).AddTicks(4368), "System", 9, true, null, null, "text" },
                    { 107, "mediumlabel", 2, new DateTime(2025, 7, 22, 5, 47, 0, 650, DateTimeKind.Utc).AddTicks(4370), "System", 9, true, null, null, "text" },
                    { 108, "regex", 3, new DateTime(2025, 7, 22, 5, 47, 0, 650, DateTimeKind.Utc).AddTicks(4372), "System", 9, true, null, null, "regex" },
                    { 109, "description", 1, new DateTime(2025, 7, 22, 5, 47, 0, 650, DateTimeKind.Utc).AddTicks(4374), "System", 9, true, null, null, "text" },
                    { 110, "stronglabel", 2, new DateTime(2025, 7, 22, 5, 47, 0, 650, DateTimeKind.Utc).AddTicks(4376), "System", 9, true, null, null, "text" },
                    { 111, "is-visible", 1, new DateTime(2025, 7, 22, 5, 47, 0, 650, DateTimeKind.Utc).AddTicks(4378), "System", 9, true, null, null, "boolean" },
                    { 112, "toggle(true/false)", 2, new DateTime(2025, 7, 22, 5, 47, 0, 650, DateTimeKind.Utc).AddTicks(4380), "System", 9, true, null, null, "boolean" },
                    { 113, "floatlable_(true/false)", 2, new DateTime(2025, 7, 22, 5, 47, 0, 650, DateTimeKind.Utc).AddTicks(4382), "System", 9, true, null, null, "boolean" },
                    { 114, "variant_(filled)", 2, new DateTime(2025, 7, 22, 5, 47, 0, 650, DateTimeKind.Utc).AddTicks(4384), "System", 9, true, null, null, "string" },
                    { 115, "invalid_(true/false)", 2, new DateTime(2025, 7, 22, 5, 47, 0, 650, DateTimeKind.Utc).AddTicks(4386), "System", 9, true, null, null, "boolean" },
                    { 116, "disabled__(true/false)", 2, new DateTime(2025, 7, 22, 5, 47, 0, 650, DateTimeKind.Utc).AddTicks(4388), "System", 9, true, null, null, "boolean" },
                    { 117, "icon", 2, new DateTime(2025, 7, 22, 5, 47, 0, 650, DateTimeKind.Utc).AddTicks(4390), "System", 9, true, null, null, "icon" },
                    { 118, "dependon", 1, new DateTime(2025, 7, 22, 5, 47, 0, 650, DateTimeKind.Utc).AddTicks(4392), "System", 10, true, null, null, "text" },
                    { 119, "dropdown", 2, new DateTime(2025, 7, 22, 5, 47, 0, 650, DateTimeKind.Utc).AddTicks(4394), "System", 10, true, null, null, "options" },
                    { 120, "unique-id", 1, new DateTime(2025, 7, 22, 5, 47, 0, 650, DateTimeKind.Utc).AddTicks(4396), "System", 10, true, null, null, "text" },
                    { 121, "display-name", 1, new DateTime(2025, 7, 22, 5, 47, 0, 650, DateTimeKind.Utc).AddTicks(4398), "System", 10, true, null, null, "text" },
                    { 122, "object", 2, new DateTime(2025, 7, 22, 5, 47, 0, 650, DateTimeKind.Utc).AddTicks(4400), "System", 10, true, null, null, "object" },
                    { 123, "name", 1, new DateTime(2025, 7, 22, 5, 47, 0, 650, DateTimeKind.Utc).AddTicks(4402), "System", 10, true, null, null, "text" },
                    { 124, "group", 2, new DateTime(2025, 7, 22, 5, 47, 0, 650, DateTimeKind.Utc).AddTicks(4404), "System", 10, true, null, null, "text" },
                    { 125, "description", 1, new DateTime(2025, 7, 22, 5, 47, 0, 650, DateTimeKind.Utc).AddTicks(4406), "System", 10, true, null, null, "text" },
                    { 126, "force_selection", 2, new DateTime(2025, 7, 22, 5, 47, 0, 650, DateTimeKind.Utc).AddTicks(4408), "System", 10, true, null, null, "boolean" },
                    { 127, "is-visible", 1, new DateTime(2025, 7, 22, 5, 47, 0, 650, DateTimeKind.Utc).AddTicks(4410), "System", 10, true, null, null, "boolean" },
                    { 128, "multiple", 2, new DateTime(2025, 7, 22, 5, 47, 0, 650, DateTimeKind.Utc).AddTicks(4412), "System", 10, true, null, null, "boolean" },
                    { 129, "floatlable_(true/false)", 2, new DateTime(2025, 7, 22, 5, 47, 0, 650, DateTimeKind.Utc).AddTicks(4414), "System", 10, true, null, null, "boolean" },
                    { 130, "variant_(filled)", 2, new DateTime(2025, 7, 22, 5, 47, 0, 650, DateTimeKind.Utc).AddTicks(4416), "System", 10, true, null, null, "string" },
                    { 131, "invalid_(true/false)", 1, new DateTime(2025, 7, 22, 5, 47, 0, 650, DateTimeKind.Utc).AddTicks(4418), "System", 10, true, null, null, "boolean" },
                    { 132, "disabled__(true/false)", 2, new DateTime(2025, 7, 22, 5, 47, 0, 650, DateTimeKind.Utc).AddTicks(4420), "System", 10, true, null, null, "boolean" },
                    { 133, "dependon", 1, new DateTime(2025, 7, 22, 5, 47, 0, 650, DateTimeKind.Utc).AddTicks(4422), "System", 11, true, null, null, "text" },
                    { 134, "unique-id", 1, new DateTime(2025, 7, 22, 5, 47, 0, 650, DateTimeKind.Utc).AddTicks(4424), "System", 11, true, null, null, "text" },
                    { 135, "placeholder", 1, new DateTime(2025, 7, 22, 5, 47, 0, 650, DateTimeKind.Utc).AddTicks(4426), "System", 11, true, null, null, "text" },
                    { 136, "display-name", 1, new DateTime(2025, 7, 22, 5, 47, 0, 650, DateTimeKind.Utc).AddTicks(4428), "System", 11, true, null, null, "text" },
                    { 137, "float_label", 2, new DateTime(2025, 7, 22, 5, 47, 0, 650, DateTimeKind.Utc).AddTicks(4430), "System", 11, true, null, null, "boolean" },
                    { 138, "name", 1, new DateTime(2025, 7, 22, 5, 47, 0, 650, DateTimeKind.Utc).AddTicks(4432), "System", 11, true, null, null, "text" },
                    { 139, "variant_(filled)", 2, new DateTime(2025, 7, 22, 5, 47, 0, 650, DateTimeKind.Utc).AddTicks(4434), "System", 11, true, null, null, "string" },
                    { 140, "description", 1, new DateTime(2025, 7, 22, 5, 47, 0, 650, DateTimeKind.Utc).AddTicks(4436), "System", 11, true, null, null, "text" },
                    { 141, "invalid", 2, new DateTime(2025, 7, 22, 5, 47, 0, 650, DateTimeKind.Utc).AddTicks(4438), "System", 11, true, null, null, "boolean" },
                    { 142, "is-visible", 1, new DateTime(2025, 7, 22, 5, 47, 0, 650, DateTimeKind.Utc).AddTicks(4440), "System", 11, true, null, null, "boolean" },
                    { 143, "disabled", 2, new DateTime(2025, 7, 22, 5, 47, 0, 650, DateTimeKind.Utc).AddTicks(4442), "System", 11, true, null, null, "boolean" },
                    { 144, "dependOn", 1, new DateTime(2025, 7, 22, 5, 47, 0, 650, DateTimeKind.Utc).AddTicks(4444), "System", 12, true, null, null, "text" },
                    { 145, "unique-id", 1, new DateTime(2025, 7, 22, 5, 47, 0, 650, DateTimeKind.Utc).AddTicks(4446), "System", 12, true, null, null, "text" },
                    { 146, "display-name", 1, new DateTime(2025, 7, 22, 5, 47, 0, 650, DateTimeKind.Utc).AddTicks(4448), "System", 12, true, null, null, "text" },
                    { 147, "name", 1, new DateTime(2025, 7, 22, 5, 47, 0, 650, DateTimeKind.Utc).AddTicks(4450), "System", 12, true, null, null, "text" },
                    { 148, "description", 1, new DateTime(2025, 7, 22, 5, 47, 0, 650, DateTimeKind.Utc).AddTicks(4452), "System", 12, true, null, null, "text" },
                    { 149, "is-visible", 1, new DateTime(2025, 7, 22, 5, 47, 0, 650, DateTimeKind.Utc).AddTicks(4454), "System", 12, true, null, null, "boolean" },
                    { 150, "get-api-url", 1, new DateTime(2025, 7, 22, 5, 47, 0, 650, DateTimeKind.Utc).AddTicks(4456), "System", 12, true, null, null, "url" },
                    { 151, "placeholder", 1, new DateTime(2025, 7, 22, 5, 47, 0, 650, DateTimeKind.Utc).AddTicks(4458), "System", 12, true, null, null, "text" },
                    { 152, "checkmark", 2, new DateTime(2025, 7, 22, 5, 47, 0, 650, DateTimeKind.Utc).AddTicks(4460), "System", 12, true, null, null, "boolean" },
                    { 153, "highlightOnSelect", 2, new DateTime(2025, 7, 22, 5, 47, 0, 650, DateTimeKind.Utc).AddTicks(4462), "System", 12, true, null, null, "boolean" },
                    { 154, "editable", 2, new DateTime(2025, 7, 22, 5, 47, 0, 650, DateTimeKind.Utc).AddTicks(4464), "System", 12, true, null, null, "boolean" },
                    { 155, "optionGroupLabel", 2, new DateTime(2025, 7, 22, 5, 47, 0, 650, DateTimeKind.Utc).AddTicks(4466), "System", 12, true, null, null, "text" },
                    { 156, "optionGroupChildren", 2, new DateTime(2025, 7, 22, 5, 47, 0, 650, DateTimeKind.Utc).AddTicks(4468), "System", 12, true, null, null, "options" },
                    { 157, "optionGroupTemplate", 2, new DateTime(2025, 7, 22, 5, 47, 0, 650, DateTimeKind.Utc).AddTicks(4470), "System", 12, true, null, null, "template" },
                    { 158, "valueTemplate", 2, new DateTime(2025, 7, 22, 5, 47, 0, 650, DateTimeKind.Utc).AddTicks(4472), "System", 12, true, null, null, "template" },
                    { 159, "itemTemplate", 2, new DateTime(2025, 7, 22, 5, 47, 0, 650, DateTimeKind.Utc).AddTicks(4474), "System", 12, true, null, null, "template" },
                    { 160, "panelFooterTemplate", 2, new DateTime(2025, 7, 22, 5, 47, 0, 650, DateTimeKind.Utc).AddTicks(4476), "System", 12, true, null, null, "template" },
                    { 161, "filter", 2, new DateTime(2025, 7, 22, 5, 47, 0, 650, DateTimeKind.Utc).AddTicks(4478), "System", 12, true, null, null, "boolean" },
                    { 162, "showClear", 2, new DateTime(2025, 7, 22, 5, 47, 0, 650, DateTimeKind.Utc).AddTicks(4480), "System", 12, true, null, null, "boolean" },
                    { 163, "loading", 2, new DateTime(2025, 7, 22, 5, 47, 0, 650, DateTimeKind.Utc).AddTicks(4482), "System", 12, true, null, null, "boolean" },
                    { 164, "virtualScrollerOptions", 2, new DateTime(2025, 7, 22, 5, 47, 0, 650, DateTimeKind.Utc).AddTicks(4484), "System", 12, true, null, null, "boolean" },
                    { 165, "FloatLable", 2, new DateTime(2025, 7, 22, 5, 47, 0, 650, DateTimeKind.Utc).AddTicks(4486), "System", 12, true, null, null, "text" },
                    { 166, "variant", 2, new DateTime(2025, 7, 22, 5, 47, 0, 650, DateTimeKind.Utc).AddTicks(4488), "System", 12, true, null, null, "options" },
                    { 167, "invalid", 2, new DateTime(2025, 7, 22, 5, 47, 0, 650, DateTimeKind.Utc).AddTicks(4490), "System", 12, true, null, null, "boolean" },
                    { 168, "disabled", 2, new DateTime(2025, 7, 22, 5, 47, 0, 650, DateTimeKind.Utc).AddTicks(4492), "System", 12, true, null, null, "boolean" },
                    { 169, "dependOn", 1, new DateTime(2025, 7, 22, 5, 47, 0, 650, DateTimeKind.Utc).AddTicks(4494), "System", 13, true, null, null, "text" },
                    { 170, "unique-id", 1, new DateTime(2025, 7, 22, 5, 47, 0, 650, DateTimeKind.Utc).AddTicks(4496), "System", 13, true, null, null, "text" },
                    { 171, "display-name", 1, new DateTime(2025, 7, 22, 5, 47, 0, 650, DateTimeKind.Utc).AddTicks(4498), "System", 13, true, null, null, "text" },
                    { 172, "name", 1, new DateTime(2025, 7, 22, 5, 47, 0, 650, DateTimeKind.Utc).AddTicks(4500), "System", 13, true, null, null, "text" },
                    { 173, "description", 1, new DateTime(2025, 7, 22, 5, 47, 0, 650, DateTimeKind.Utc).AddTicks(4502), "System", 13, true, null, null, "text" },
                    { 174, "is-visible", 1, new DateTime(2025, 7, 22, 5, 47, 0, 650, DateTimeKind.Utc).AddTicks(4504), "System", 13, true, null, null, "boolean" },
                    { 175, "get-api-url", 1, new DateTime(2025, 7, 22, 5, 47, 0, 650, DateTimeKind.Utc).AddTicks(4506), "System", 13, true, null, null, "url" },
                    { 176, "mode", 2, new DateTime(2025, 7, 22, 5, 47, 0, 650, DateTimeKind.Utc).AddTicks(4508), "System", 13, true, null, null, "options" },
                    { 177, "url", 2, new DateTime(2025, 7, 22, 5, 47, 0, 650, DateTimeKind.Utc).AddTicks(4510), "System", 13, true, null, null, "url" },
                    { 178, "accept", 2, new DateTime(2025, 7, 22, 5, 47, 0, 650, DateTimeKind.Utc).AddTicks(4512), "System", 13, true, null, null, "upload" },
                    { 179, "minFileSize", 2, new DateTime(2025, 7, 22, 5, 47, 0, 650, DateTimeKind.Utc).AddTicks(4514), "System", 13, true, null, null, "number" },
                    { 180, "maxFileSize", 2, new DateTime(2025, 7, 22, 5, 47, 0, 650, DateTimeKind.Utc).AddTicks(4516), "System", 13, true, null, null, "number" },
                    { 181, "auto", 2, new DateTime(2025, 7, 22, 5, 47, 0, 650, DateTimeKind.Utc).AddTicks(4518), "System", 13, true, null, null, "boolean" },
                    { 182, "chooseLabel", 2, new DateTime(2025, 7, 22, 5, 47, 0, 650, DateTimeKind.Utc).AddTicks(4520), "System", 13, true, null, null, "text" },
                    { 183, "multiple", 2, new DateTime(2025, 7, 22, 5, 47, 0, 650, DateTimeKind.Utc).AddTicks(4522), "System", 13, true, null, null, "boolean" },
                    { 184, "emptyTemplate", 2, new DateTime(2025, 7, 22, 5, 47, 0, 650, DateTimeKind.Utc).AddTicks(4524), "System", 13, true, null, null, "template" },
                    { 185, "customUpload", 2, new DateTime(2025, 7, 22, 5, 47, 0, 650, DateTimeKind.Utc).AddTicks(4526), "System", 13, true, null, null, "boolean" },
                    { 186, "uploadHandler", 2, new DateTime(2025, 7, 22, 5, 47, 0, 650, DateTimeKind.Utc).AddTicks(4528), "System", 13, true, null, null, "upload" },
                    { 187, "dependOn", 1, new DateTime(2025, 7, 22, 5, 47, 0, 650, DateTimeKind.Utc).AddTicks(4530), "System", 14, true, null, null, "text" },
                    { 188, "unique-id", 1, new DateTime(2025, 7, 22, 5, 47, 0, 650, DateTimeKind.Utc).AddTicks(4532), "System", 14, true, null, null, "text" },
                    { 189, "display-name", 1, new DateTime(2025, 7, 22, 5, 47, 0, 650, DateTimeKind.Utc).AddTicks(4534), "System", 14, true, null, null, "text" },
                    { 190, "name", 1, new DateTime(2025, 7, 22, 5, 47, 0, 650, DateTimeKind.Utc).AddTicks(4536), "System", 14, true, null, null, "text" },
                    { 191, "description", 1, new DateTime(2025, 7, 22, 5, 47, 0, 650, DateTimeKind.Utc).AddTicks(4538), "System", 14, true, null, null, "text" },
                    { 192, "is-visible", 1, new DateTime(2025, 7, 22, 5, 47, 0, 650, DateTimeKind.Utc).AddTicks(4540), "System", 14, true, null, null, "boolean" },
                    { 193, "options", 2, new DateTime(2025, 7, 22, 5, 47, 0, 650, DateTimeKind.Utc).AddTicks(4542), "System", 14, true, null, null, "array" },
                    { 194, "iconTemplate", 2, new DateTime(2025, 7, 22, 5, 47, 0, 650, DateTimeKind.Utc).AddTicks(4544), "System", 14, true, null, null, "template" },
                    { 195, "disabled", 2, new DateTime(2025, 7, 22, 5, 47, 0, 650, DateTimeKind.Utc).AddTicks(4546), "System", 14, true, null, null, "boolean" },
                    { 196, "value", 2, new DateTime(2025, 7, 22, 5, 47, 0, 650, DateTimeKind.Utc).AddTicks(4548), "System", 14, true, null, null, "object" },
                    { 197, "optionValue", 2, new DateTime(2025, 7, 22, 5, 47, 0, 650, DateTimeKind.Utc).AddTicks(4550), "System", 14, true, null, null, "options" },
                    { 198, "required", 1, new DateTime(2025, 7, 22, 5, 47, 0, 650, DateTimeKind.Utc).AddTicks(4552), "System", 14, true, null, null, "boolean" },
                    { 199, "dependOn", 1, new DateTime(2025, 7, 22, 5, 47, 0, 650, DateTimeKind.Utc).AddTicks(4554), "System", 15, true, null, null, "text" },
                    { 200, "unique-id", 1, new DateTime(2025, 7, 22, 5, 47, 0, 650, DateTimeKind.Utc).AddTicks(4556), "System", 15, true, null, null, "text" },
                    { 201, "display-name", 1, new DateTime(2025, 7, 22, 5, 47, 0, 650, DateTimeKind.Utc).AddTicks(4558), "System", 15, true, null, null, "text" },
                    { 202, "name", 1, new DateTime(2025, 7, 22, 5, 47, 0, 650, DateTimeKind.Utc).AddTicks(4560), "System", 15, true, null, null, "text" },
                    { 203, "description", 1, new DateTime(2025, 7, 22, 5, 47, 0, 650, DateTimeKind.Utc).AddTicks(4562), "System", 15, true, null, null, "text" },
                    { 204, "is-visible", 1, new DateTime(2025, 7, 22, 5, 47, 0, 650, DateTimeKind.Utc).AddTicks(4564), "System", 15, true, null, null, "boolean" },
                    { 205, "options", 2, new DateTime(2025, 7, 22, 5, 47, 0, 650, DateTimeKind.Utc).AddTicks(4566), "System", 15, true, null, null, "array" },
                    { 206, "placeholder", 1, new DateTime(2025, 7, 22, 5, 47, 0, 650, DateTimeKind.Utc).AddTicks(4568), "System", 15, true, null, null, "text" },
                    { 207, "display", 2, new DateTime(2025, 7, 22, 5, 47, 0, 650, DateTimeKind.Utc).AddTicks(4570), "System", 15, true, null, null, "options" },
                    { 208, "optionLabel", 2, new DateTime(2025, 7, 22, 5, 47, 0, 650, DateTimeKind.Utc).AddTicks(4572), "System", 15, true, null, null, "text" },
                    { 209, "maxSelectedLabels", 2, new DateTime(2025, 7, 22, 5, 47, 0, 650, DateTimeKind.Utc).AddTicks(4574), "System", 15, true, null, null, "number" },
                    { 210, "optionGroupLabel", 2, new DateTime(2025, 7, 22, 5, 47, 0, 650, DateTimeKind.Utc).AddTicks(4576), "System", 15, true, null, null, "text" },
                    { 211, "optionGroupChildren", 2, new DateTime(2025, 7, 22, 5, 47, 0, 650, DateTimeKind.Utc).AddTicks(4578), "System", 15, true, null, null, "options" },
                    { 212, "optionGroupTemplate", 2, new DateTime(2025, 7, 22, 5, 47, 0, 650, DateTimeKind.Utc).AddTicks(4580), "System", 15, true, null, null, "template" },
                    { 213, "panelFooterTemplate", 2, new DateTime(2025, 7, 22, 5, 47, 0, 650, DateTimeKind.Utc).AddTicks(4582), "System", 15, true, null, null, "template" },
                    { 214, "itemTemplate", 2, new DateTime(2025, 7, 22, 5, 47, 0, 650, DateTimeKind.Utc).AddTicks(4584), "System", 15, true, null, null, "template" },
                    { 215, "filter", 2, new DateTime(2025, 7, 22, 5, 47, 0, 650, DateTimeKind.Utc).AddTicks(4586), "System", 15, true, null, null, "boolean" },
                    { 216, "loading", 2, new DateTime(2025, 7, 22, 5, 47, 0, 650, DateTimeKind.Utc).AddTicks(4588), "System", 15, true, null, null, "boolean" },
                    { 217, "FloatLable", 2, new DateTime(2025, 7, 22, 5, 47, 0, 650, DateTimeKind.Utc).AddTicks(4590), "System", 15, true, null, null, "text" },
                    { 218, "variant", 2, new DateTime(2025, 7, 22, 5, 47, 0, 650, DateTimeKind.Utc).AddTicks(4592), "System", 15, true, null, null, "options" },
                    { 219, "invalid", 2, new DateTime(2025, 7, 22, 5, 47, 0, 650, DateTimeKind.Utc).AddTicks(4594), "System", 15, true, null, null, "boolean" },
                    { 220, "disabled", 2, new DateTime(2025, 7, 22, 5, 47, 0, 650, DateTimeKind.Utc).AddTicks(4596), "System", 15, true, null, null, "boolean" },
                    { 221, "required", 3, new DateTime(2025, 7, 22, 5, 47, 0, 650, DateTimeKind.Utc).AddTicks(4598), "System", 15, true, null, null, "boolean" },
                    { 222, "dependOn", 1, new DateTime(2025, 7, 22, 5, 47, 0, 650, DateTimeKind.Utc).AddTicks(4600), "System", 16, true, null, null, "text" },
                    { 223, "unique-id", 1, new DateTime(2025, 7, 22, 5, 47, 0, 650, DateTimeKind.Utc).AddTicks(4602), "System", 16, true, null, null, "text" },
                    { 224, "display-name", 1, new DateTime(2025, 7, 22, 5, 47, 0, 650, DateTimeKind.Utc).AddTicks(4604), "System", 16, true, null, null, "text" },
                    { 225, "name", 1, new DateTime(2025, 7, 22, 5, 47, 0, 650, DateTimeKind.Utc).AddTicks(4606), "System", 16, true, null, null, "text" },
                    { 226, "description", 1, new DateTime(2025, 7, 22, 5, 47, 0, 650, DateTimeKind.Utc).AddTicks(4608), "System", 16, true, null, null, "text" },
                    { 227, "is-visible", 3, new DateTime(2025, 7, 22, 5, 47, 0, 650, DateTimeKind.Utc).AddTicks(4610), "System", 16, true, null, null, "boolean" },
                    { 228, "field", 2, new DateTime(2025, 7, 22, 5, 47, 0, 650, DateTimeKind.Utc).AddTicks(4612), "System", 16, true, null, null, "string" },
                    { 229, "placeholder", 2, new DateTime(2025, 7, 22, 5, 47, 0, 650, DateTimeKind.Utc).AddTicks(4614), "System", 16, true, null, null, "text" },
                    { 230, "rows", 2, new DateTime(2025, 7, 22, 5, 47, 0, 650, DateTimeKind.Utc).AddTicks(4616), "System", 16, true, null, null, "number" },
                    { 231, "cols", 2, new DateTime(2025, 7, 22, 5, 47, 0, 650, DateTimeKind.Utc).AddTicks(4618), "System", 16, true, null, null, "number" },
                    { 232, "trigger", 2, new DateTime(2025, 7, 22, 5, 47, 0, 650, DateTimeKind.Utc).AddTicks(4620), "System", 16, true, null, null, "options" },
                    { 233, "autoResize", 2, new DateTime(2025, 7, 22, 5, 47, 0, 650, DateTimeKind.Utc).AddTicks(4622), "System", 16, true, null, null, "code" },
                    { 234, "FloatLabel", 2, new DateTime(2025, 7, 22, 5, 47, 0, 650, DateTimeKind.Utc).AddTicks(4624), "System", 16, true, null, null, "string" },
                    { 235, "variant (Filled)", 2, new DateTime(2025, 7, 22, 5, 47, 0, 650, DateTimeKind.Utc).AddTicks(4626), "System", 16, true, null, null, "string" },
                    { 236, "Disabled", 2, new DateTime(2025, 7, 22, 5, 47, 0, 650, DateTimeKind.Utc).AddTicks(4628), "System", 16, true, null, null, "boolean" },
                    { 237, "invalid (True/false)", 2, new DateTime(2025, 7, 22, 5, 47, 0, 650, DateTimeKind.Utc).AddTicks(4630), "System", 16, true, null, null, "boolean" },
                    { 238, "dependOn", 1, new DateTime(2025, 7, 22, 5, 47, 0, 650, DateTimeKind.Utc).AddTicks(4632), "System", 17, true, null, null, "text" },
                    { 239, "unique-id", 1, new DateTime(2025, 7, 22, 5, 47, 0, 650, DateTimeKind.Utc).AddTicks(4634), "System", 17, true, null, null, "text" },
                    { 240, "display-name", 1, new DateTime(2025, 7, 22, 5, 47, 0, 650, DateTimeKind.Utc).AddTicks(4636), "System", 17, true, null, null, "text" },
                    { 241, "name", 1, new DateTime(2025, 7, 22, 5, 47, 0, 650, DateTimeKind.Utc).AddTicks(4638), "System", 17, true, null, null, "text" },
                    { 242, "description", 1, new DateTime(2025, 7, 22, 5, 47, 0, 650, DateTimeKind.Utc).AddTicks(4640), "System", 17, true, null, null, "text" },
                    { 243, "is-visible", 1, new DateTime(2025, 7, 22, 5, 47, 0, 650, DateTimeKind.Utc).AddTicks(4642), "System", 17, true, null, null, "boolean" },
                    { 244, "rows", 2, new DateTime(2025, 7, 22, 5, 47, 0, 650, DateTimeKind.Utc).AddTicks(4644), "System", 17, true, null, null, "number" },
                    { 245, "cols", 2, new DateTime(2025, 7, 22, 5, 47, 0, 650, DateTimeKind.Utc).AddTicks(4646), "System", 17, true, null, null, "number" },
                    { 246, "autoResize", 2, new DateTime(2025, 7, 22, 5, 47, 0, 650, DateTimeKind.Utc).AddTicks(4648), "System", 17, true, null, null, "code" },
                    { 247, "placeholder", 2, new DateTime(2025, 7, 22, 5, 47, 0, 650, DateTimeKind.Utc).AddTicks(4650), "System", 17, true, null, null, "text" },
                    { 248, "Key Filter", 2, new DateTime(2025, 7, 22, 5, 47, 0, 650, DateTimeKind.Utc).AddTicks(4652), "System", 17, true, null, null, "string" },
                    { 249, "FloatLabel", 2, new DateTime(2025, 7, 22, 5, 47, 0, 650, DateTimeKind.Utc).AddTicks(4654), "System", 17, true, null, null, "boolean" },
                    { 250, "variant (Filled)", 2, new DateTime(2025, 7, 22, 5, 47, 0, 650, DateTimeKind.Utc).AddTicks(4656), "System", 17, true, null, null, "string" },
                    { 251, "Disabled", 2, new DateTime(2025, 7, 22, 5, 47, 0, 650, DateTimeKind.Utc).AddTicks(4658), "System", 17, true, null, null, "boolean" },
                    { 252, "invalid (True/false)", 2, new DateTime(2025, 7, 22, 5, 47, 0, 650, DateTimeKind.Utc).AddTicks(4660), "System", 17, true, null, null, "boolean" },
                    { 253, "required", 3, new DateTime(2025, 7, 22, 5, 47, 0, 650, DateTimeKind.Utc).AddTicks(4662), "System", 17, true, null, null, "boolean" },
                    { 254, "minLength", 2, new DateTime(2025, 7, 22, 5, 47, 0, 650, DateTimeKind.Utc).AddTicks(4664), "System", 17, true, null, null, "number" },
                    { 255, "maxLength", 2, new DateTime(2025, 7, 22, 5, 47, 0, 650, DateTimeKind.Utc).AddTicks(4666), "System", 17, true, null, null, "number" },
                    { 256, "dependOn", 1, new DateTime(2025, 7, 22, 5, 47, 0, 650, DateTimeKind.Utc).AddTicks(4668), "System", 18, true, null, null, "text" },
                    { 257, "unique-id", 1, new DateTime(2025, 7, 22, 5, 47, 0, 650, DateTimeKind.Utc).AddTicks(4670), "System", 18, true, null, null, "text" },
                    { 258, "display-name", 1, new DateTime(2025, 7, 22, 5, 47, 0, 650, DateTimeKind.Utc).AddTicks(4672), "System", 18, true, null, null, "text" },
                    { 259, "name", 1, new DateTime(2025, 7, 22, 5, 47, 0, 650, DateTimeKind.Utc).AddTicks(4674), "System", 18, true, null, null, "text" },
                    { 260, "description", 1, new DateTime(2025, 7, 22, 5, 47, 0, 650, DateTimeKind.Utc).AddTicks(4676), "System", 18, true, null, null, "text" },
                    { 261, "is-visible", 1, new DateTime(2025, 7, 22, 5, 47, 0, 650, DateTimeKind.Utc).AddTicks(4678), "System", 18, true, null, null, "boolean" },
                    { 262, "mask", 3, new DateTime(2025, 7, 22, 5, 47, 0, 650, DateTimeKind.Utc).AddTicks(4680), "System", 18, true, null, null, "regex" },
                    { 263, "required", 3, new DateTime(2025, 7, 22, 5, 47, 0, 650, DateTimeKind.Utc).AddTicks(4682), "System", 18, true, null, null, "boolean" },
                    { 264, "integerOnly", 2, new DateTime(2025, 7, 22, 5, 47, 0, 650, DateTimeKind.Utc).AddTicks(4684), "System", 18, true, null, null, "boolean" },
                    { 265, "inputTemplate", 2, new DateTime(2025, 7, 22, 5, 47, 0, 650, DateTimeKind.Utc).AddTicks(4686), "System", 18, true, null, null, "template" },
                    { 266, "optionLabel", 2, new DateTime(2025, 7, 22, 5, 47, 0, 650, DateTimeKind.Utc).AddTicks(4688), "System", 12, true, null, null, "text" },
                    { 267, "optionValue", 2, new DateTime(2025, 7, 22, 5, 47, 0, 650, DateTimeKind.Utc).AddTicks(4690), "System", 12, true, null, null, "text" },
                    { 268, "dependOn", 1, new DateTime(2025, 7, 22, 5, 47, 0, 650, DateTimeKind.Utc).AddTicks(4692), "System", 19, true, null, null, "string" },
                    { 269, "required", 3, new DateTime(2025, 7, 22, 5, 47, 0, 650, DateTimeKind.Utc).AddTicks(4694), "System", 19, true, null, null, "boolean" },
                    { 270, "unique-id", 1, new DateTime(2025, 7, 22, 5, 47, 0, 650, DateTimeKind.Utc).AddTicks(4696), "System", 19, true, null, null, "string" },
                    { 271, "api-id", 2, new DateTime(2025, 7, 22, 5, 47, 0, 650, DateTimeKind.Utc).AddTicks(4698), "System", 19, true, null, null, "string" },
                    { 272, "display-name", 1, new DateTime(2025, 7, 22, 5, 47, 0, 650, DateTimeKind.Utc).AddTicks(4700), "System", 19, true, null, null, "string" },
                    { 273, "name", 1, new DateTime(2025, 7, 22, 5, 47, 0, 650, DateTimeKind.Utc).AddTicks(4702), "System", 19, true, null, null, "string" },
                    { 274, "description", 1, new DateTime(2025, 7, 22, 5, 47, 0, 650, DateTimeKind.Utc).AddTicks(4704), "System", 19, true, null, null, "string" },
                    { 275, "display-preference", 2, new DateTime(2025, 7, 22, 5, 47, 0, 650, DateTimeKind.Utc).AddTicks(4706), "System", 19, true, null, null, "string" },
                    { 276, "is-visible", 1, new DateTime(2025, 7, 22, 5, 47, 0, 650, DateTimeKind.Utc).AddTicks(4708), "System", 19, true, null, null, "boolean" },
                    { 277, "type", 2, new DateTime(2025, 7, 22, 5, 47, 0, 650, DateTimeKind.Utc).AddTicks(4710), "System", 19, true, null, null, "string" },
                    { 278, "dependOn", 1, new DateTime(2025, 7, 22, 5, 47, 0, 650, DateTimeKind.Utc).AddTicks(4712), "System", 20, true, null, null, "string" },
                    { 279, "required", 3, new DateTime(2025, 7, 22, 5, 47, 0, 650, DateTimeKind.Utc).AddTicks(4714), "System", 20, true, null, null, "boolean" },
                    { 280, "unique-id", 1, new DateTime(2025, 7, 22, 5, 47, 0, 650, DateTimeKind.Utc).AddTicks(4716), "System", 20, true, null, null, "string" },
                    { 281, "api-id", 2, new DateTime(2025, 7, 22, 5, 47, 0, 650, DateTimeKind.Utc).AddTicks(4718), "System", 20, true, null, null, "string" },
                    { 282, "display-name", 1, new DateTime(2025, 7, 22, 5, 47, 0, 650, DateTimeKind.Utc).AddTicks(4720), "System", 20, true, null, null, "string" },
                    { 283, "name", 1, new DateTime(2025, 7, 22, 5, 47, 0, 650, DateTimeKind.Utc).AddTicks(4722), "System", 20, true, null, null, "string" },
                    { 284, "description", 1, new DateTime(2025, 7, 22, 5, 47, 0, 650, DateTimeKind.Utc).AddTicks(4724), "System", 20, true, null, null, "string" },
                    { 285, "display-preference", 2, new DateTime(2025, 7, 22, 5, 47, 0, 650, DateTimeKind.Utc).AddTicks(4726), "System", 20, true, null, null, "string" },
                    { 286, "is-visible", 1, new DateTime(2025, 7, 22, 5, 47, 0, 650, DateTimeKind.Utc).AddTicks(4728), "System", 20, true, null, null, "boolean" },
                    { 287, "type", 2, new DateTime(2025, 7, 22, 5, 47, 0, 650, DateTimeKind.Utc).AddTicks(4730), "System", 20, true, null, null, "string" },
                    { 288, "dependOn", 1, new DateTime(2025, 7, 22, 5, 47, 0, 650, DateTimeKind.Utc).AddTicks(4732), "System", 21, true, null, null, "string" },
                    { 289, "required", 3, new DateTime(2025, 7, 22, 5, 47, 0, 650, DateTimeKind.Utc).AddTicks(4734), "System", 21, true, null, null, "boolean" },
                    { 290, "unique-id", 1, new DateTime(2025, 7, 22, 5, 47, 0, 650, DateTimeKind.Utc).AddTicks(4736), "System", 21, true, null, null, "string" },
                    { 291, "api-id", 2, new DateTime(2025, 7, 22, 5, 47, 0, 650, DateTimeKind.Utc).AddTicks(4738), "System", 21, true, null, null, "string" },
                    { 292, "display-name", 1, new DateTime(2025, 7, 22, 5, 47, 0, 650, DateTimeKind.Utc).AddTicks(4740), "System", 21, true, null, null, "string" },
                    { 293, "name", 1, new DateTime(2025, 7, 22, 5, 47, 0, 650, DateTimeKind.Utc).AddTicks(4742), "System", 21, true, null, null, "string" },
                    { 294, "description", 1, new DateTime(2025, 7, 22, 5, 47, 0, 650, DateTimeKind.Utc).AddTicks(4744), "System", 21, true, null, null, "string" },
                    { 295, "display-preference", 2, new DateTime(2025, 7, 22, 5, 47, 0, 650, DateTimeKind.Utc).AddTicks(4746), "System", 21, true, null, null, "string" },
                    { 296, "is-visible", 1, new DateTime(2025, 7, 22, 5, 47, 0, 650, DateTimeKind.Utc).AddTicks(4748), "System", 21, true, null, null, "boolean" },
                    { 297, "type", 2, new DateTime(2025, 7, 22, 5, 47, 0, 650, DateTimeKind.Utc).AddTicks(4750), "System", 21, true, null, null, "string" },
                    { 298, "Group", 2, new DateTime(2025, 7, 22, 5, 47, 0, 650, DateTimeKind.Utc).AddTicks(4752), "System", 19, true, null, null, "string" },
                    { 299, "Dynamic", 2, new DateTime(2025, 7, 22, 5, 47, 0, 650, DateTimeKind.Utc).AddTicks(4754), "System", 19, true, null, null, "boolean" },
                    { 300, "Invalid (True/false)", 2, new DateTime(2025, 7, 22, 5, 47, 0, 650, DateTimeKind.Utc).AddTicks(4756), "System", 19, true, null, null, "boolean" },
                    { 301, "Filled", 2, new DateTime(2025, 7, 22, 5, 47, 0, 650, DateTimeKind.Utc).AddTicks(4758), "System", 19, true, null, null, "string" },
                    { 302, "Disabled", 2, new DateTime(2025, 7, 22, 5, 47, 0, 650, DateTimeKind.Utc).AddTicks(4760), "System", 19, true, null, null, "boolean" },
                    { 303, "Accessibility", 2, new DateTime(2025, 7, 22, 5, 47, 0, 650, DateTimeKind.Utc).AddTicks(4762), "System", 19, true, null, null, "string" },
                    { 304, "Group", 2, new DateTime(2025, 7, 22, 5, 47, 0, 650, DateTimeKind.Utc).AddTicks(4764), "System", 20, true, null, null, "string" },
                    { 305, "Dynamic", 2, new DateTime(2025, 7, 22, 5, 47, 0, 650, DateTimeKind.Utc).AddTicks(4766), "System", 20, true, null, null, "boolean" },
                    { 306, "Invalid (True/false)", 2, new DateTime(2025, 7, 22, 5, 47, 0, 650, DateTimeKind.Utc).AddTicks(4768), "System", 20, true, null, null, "boolean" },
                    { 307, "Filled", 2, new DateTime(2025, 7, 22, 5, 47, 0, 650, DateTimeKind.Utc).AddTicks(4770), "System", 20, true, null, null, "string" },
                    { 308, "Disabled", 2, new DateTime(2025, 7, 22, 5, 47, 0, 650, DateTimeKind.Utc).AddTicks(4772), "System", 20, true, null, null, "boolean" },
                    { 309, "Accessibility", 2, new DateTime(2025, 7, 22, 5, 47, 0, 650, DateTimeKind.Utc).AddTicks(4774), "System", 20, true, null, null, "string" },
                    { 310, "Preselection", 2, new DateTime(2025, 7, 22, 5, 47, 0, 650, DateTimeKind.Utc).AddTicks(4776), "System", 21, true, null, null, "string" },
                    { 311, "Disabled", 2, new DateTime(2025, 7, 22, 5, 47, 0, 650, DateTimeKind.Utc).AddTicks(4778), "System", 21, true, null, null, "boolean" },
                    { 312, "Invalid (True/false)", 2, new DateTime(2025, 7, 22, 5, 47, 0, 650, DateTimeKind.Utc).AddTicks(4780), "System", 21, true, null, null, "boolean" },
                    { 313, "Accessibility", 2, new DateTime(2025, 7, 22, 5, 47, 0, 650, DateTimeKind.Utc).AddTicks(4782), "System", 21, true, null, null, "string" },
                    { 315, "display-name", 1, new DateTime(2025, 7, 22, 5, 47, 0, 650, DateTimeKind.Utc).AddTicks(4784), "System", 3, true, null, null, "string" },
                    { 316, "Placeholder", 2, new DateTime(2025, 7, 22, 5, 47, 0, 650, DateTimeKind.Utc).AddTicks(4786), "System", 3, true, null, null, "string" },
                    { 317, "Name", 1, new DateTime(2025, 7, 22, 5, 47, 0, 650, DateTimeKind.Utc).AddTicks(4788), "System", 3, true, null, null, "string" },
                    { 318, "display-name", 1, new DateTime(2025, 7, 22, 5, 47, 0, 650, DateTimeKind.Utc).AddTicks(4790), "System", 6, true, null, null, "string" },
                    { 319, "display-name", 1, new DateTime(2025, 7, 22, 5, 47, 0, 650, DateTimeKind.Utc).AddTicks(4792), "System", 8, true, null, null, "string" },
                    { 320, "readOnly", 1, new DateTime(2025, 7, 22, 5, 47, 0, 650, DateTimeKind.Utc).AddTicks(4794), "System", 8, true, null, null, "boolean" },
                    { 321, "label", 2, new DateTime(2025, 7, 22, 5, 47, 0, 650, DateTimeKind.Utc).AddTicks(4796), "System", 19, true, null, null, "string" },
                    { 322, "label", 2, new DateTime(2025, 7, 22, 5, 47, 0, 650, DateTimeKind.Utc).AddTicks(4798), "System", 20, true, null, null, "string" },
                    { 323, "is-visible", 1, new DateTime(2025, 7, 22, 5, 47, 0, 650, DateTimeKind.Utc).AddTicks(4800), "System", 23, true, null, null, "boolean" },
                    { 324, "postapiurls", 1, new DateTime(2025, 7, 22, 5, 47, 0, 650, DateTimeKind.Utc).AddTicks(4802), "System", 23, true, null, null, "string" },
                    { 325, "updateapiurls", 1, new DateTime(2025, 7, 22, 5, 47, 0, 650, DateTimeKind.Utc).AddTicks(4804), "System", 23, true, null, null, "string" },
                    { 326, "postapijson", 1, new DateTime(2025, 7, 22, 5, 47, 0, 650, DateTimeKind.Utc).AddTicks(4806), "System", 23, true, null, null, "string" },
                    { 327, "updateapijson", 1, new DateTime(2025, 7, 22, 5, 47, 0, 650, DateTimeKind.Utc).AddTicks(4808), "System", 23, true, null, null, "string" },
                    { 328, "is-visible", 1, new DateTime(2025, 7, 22, 5, 47, 0, 650, DateTimeKind.Utc).AddTicks(4810), "System", 22, true, null, null, "boolean" },
                    { 329, "showButtons", 2, new DateTime(2025, 7, 22, 5, 47, 0, 650, DateTimeKind.Utc).AddTicks(4812), "System", 2, true, null, null, "boolean" },
                    { 330, "isrelative", 2, new DateTime(2025, 7, 22, 5, 47, 0, 650, DateTimeKind.Utc).AddTicks(4814), "System", 3, true, null, null, "boolean" },
                    { 331, "previnterval", 2, new DateTime(2025, 7, 22, 5, 47, 0, 650, DateTimeKind.Utc).AddTicks(4816), "System", 3, true, null, null, "integer" },
                    { 332, "nextinterval", 2, new DateTime(2025, 7, 22, 5, 47, 0, 650, DateTimeKind.Utc).AddTicks(4818), "System", 3, true, null, null, "integer" },
                    { 333, "previntervalunit", 2, new DateTime(2025, 7, 22, 5, 47, 0, 650, DateTimeKind.Utc).AddTicks(4820), "System", 3, true, null, null, "text" },
                    { 334, "nextintervalunit", 2, new DateTime(2025, 7, 22, 5, 47, 0, 650, DateTimeKind.Utc).AddTicks(4822), "System", 3, true, null, null, "text" },
                    { 335, "days", 2, new DateTime(2025, 7, 22, 5, 47, 0, 650, DateTimeKind.Utc).AddTicks(4824), "System", 3, true, null, null, "text" },
                    { 336, "weeks", 2, new DateTime(2025, 7, 22, 5, 47, 0, 650, DateTimeKind.Utc).AddTicks(4826), "System", 3, true, null, null, "text" },
                    { 337, "months", 2, new DateTime(2025, 7, 22, 5, 47, 0, 650, DateTimeKind.Utc).AddTicks(4828), "System", 3, true, null, null, "text" },
                    { 338, "year", 2, new DateTime(2025, 7, 22, 5, 47, 0, 650, DateTimeKind.Utc).AddTicks(4830), "System", 3, true, null, null, "text" },
                    { 339, "isdefault", 2, new DateTime(2025, 7, 22, 5, 47, 0, 650, DateTimeKind.Utc).AddTicks(4832), "System", 3, true, null, null, "text" },
                    { 340, "display-name", 1, new DateTime(2025, 7, 22, 5, 47, 0, 650, DateTimeKind.Utc).AddTicks(4834), "System", 3, true, null, null, "text" },
                    { 341, "dependOn", 1, new DateTime(2025, 7, 22, 5, 47, 0, 650, DateTimeKind.Utc).AddTicks(4836), "System", 24, true, null, null, "text" },
                    { 342, "unique-id", 1, new DateTime(2025, 7, 22, 5, 47, 0, 650, DateTimeKind.Utc).AddTicks(4838), "System", 24, true, null, null, "text" },
                    { 343, "display-name", 1, new DateTime(2025, 7, 22, 5, 47, 0, 650, DateTimeKind.Utc).AddTicks(4840), "System", 24, true, null, null, "text" },
                    { 344, "name", 1, new DateTime(2025, 7, 22, 5, 47, 0, 650, DateTimeKind.Utc).AddTicks(4842), "System", 24, true, null, null, "text" },
                    { 345, "description", 1, new DateTime(2025, 7, 22, 5, 47, 0, 650, DateTimeKind.Utc).AddTicks(4844), "System", 24, true, null, null, "text" },
                    { 346, "is-visible", 1, new DateTime(2025, 7, 22, 5, 47, 0, 650, DateTimeKind.Utc).AddTicks(4846), "System", 24, true, null, null, "boolean" },
                    { 347, "get-api-url", 1, new DateTime(2025, 7, 22, 5, 47, 0, 650, DateTimeKind.Utc).AddTicks(4848), "System", 24, true, null, null, "url" },
                    { 348, "placeholder", 1, new DateTime(2025, 7, 22, 5, 47, 0, 650, DateTimeKind.Utc).AddTicks(4850), "System", 24, true, null, null, "text" },
                    { 349, "checkmark", 2, new DateTime(2025, 7, 22, 5, 47, 0, 650, DateTimeKind.Utc).AddTicks(4852), "System", 24, true, null, null, "boolean" },
                    { 350, "highlightOnSelect", 2, new DateTime(2025, 7, 22, 5, 47, 0, 650, DateTimeKind.Utc).AddTicks(4854), "System", 24, true, null, null, "boolean" },
                    { 351, "editable", 2, new DateTime(2025, 7, 22, 5, 47, 0, 650, DateTimeKind.Utc).AddTicks(4856), "System", 24, true, null, null, "boolean" },
                    { 352, "optionGroupLabel", 2, new DateTime(2025, 7, 22, 5, 47, 0, 650, DateTimeKind.Utc).AddTicks(4858), "System", 24, true, null, null, "text" },
                    { 353, "optionGroupChildren", 2, new DateTime(2025, 7, 22, 5, 47, 0, 650, DateTimeKind.Utc).AddTicks(4860), "System", 24, true, null, null, "options" },
                    { 354, "optionGroupTemplate", 2, new DateTime(2025, 7, 22, 5, 47, 0, 650, DateTimeKind.Utc).AddTicks(4862), "System", 24, true, null, null, "template" },
                    { 355, "valueTemplate", 2, new DateTime(2025, 7, 22, 5, 47, 0, 650, DateTimeKind.Utc).AddTicks(4864), "System", 24, true, null, null, "template" },
                    { 356, "itemTemplate", 2, new DateTime(2025, 7, 22, 5, 47, 0, 650, DateTimeKind.Utc).AddTicks(4866), "System", 24, true, null, null, "template" },
                    { 357, "panelFooterTemplate", 2, new DateTime(2025, 7, 22, 5, 47, 0, 650, DateTimeKind.Utc).AddTicks(4868), "System", 24, true, null, null, "template" },
                    { 358, "filter", 2, new DateTime(2025, 7, 22, 5, 47, 0, 650, DateTimeKind.Utc).AddTicks(4870), "System", 24, true, null, null, "boolean" },
                    { 359, "showClear", 2, new DateTime(2025, 7, 22, 5, 47, 0, 650, DateTimeKind.Utc).AddTicks(4872), "System", 24, true, null, null, "boolean" },
                    { 360, "loading", 2, new DateTime(2025, 7, 22, 5, 47, 0, 650, DateTimeKind.Utc).AddTicks(4874), "System", 24, true, null, null, "boolean" },
                    { 361, "virtualScrollerOptions", 2, new DateTime(2025, 7, 22, 5, 47, 0, 650, DateTimeKind.Utc).AddTicks(4876), "System", 24, true, null, null, "boolean" },
                    { 362, "FloatLable", 2, new DateTime(2025, 7, 22, 5, 47, 0, 650, DateTimeKind.Utc).AddTicks(4878), "System", 24, true, null, null, "text" },
                    { 363, "variant", 2, new DateTime(2025, 7, 22, 5, 47, 0, 650, DateTimeKind.Utc).AddTicks(4880), "System", 24, true, null, null, "options" },
                    { 364, "invalid", 2, new DateTime(2025, 7, 22, 5, 47, 0, 650, DateTimeKind.Utc).AddTicks(4882), "System", 24, true, null, null, "boolean" },
                    { 365, "disabled", 2, new DateTime(2025, 7, 22, 5, 47, 0, 650, DateTimeKind.Utc).AddTicks(4884), "System", 24, true, null, null, "boolean" },
                    { 366, "optionLabel", 2, new DateTime(2025, 7, 22, 5, 47, 0, 650, DateTimeKind.Utc).AddTicks(4886), "System", 24, true, null, null, "text" },
                    { 367, "optionValue", 2, new DateTime(2025, 7, 22, 5, 47, 0, 650, DateTimeKind.Utc).AddTicks(4888), "System", 24, true, null, null, "text" },
                    { 368, "dataset", 2, new DateTime(2025, 7, 22, 5, 47, 0, 650, DateTimeKind.Utc).AddTicks(4890), "System", 24, true, null, null, "text" },
                    { 369, "value", 2, new DateTime(2025, 7, 22, 5, 47, 0, 650, DateTimeKind.Utc).AddTicks(4892), "System", 24, true, null, null, "text" },

                    // Time field configurations (FieldTypeId = 7)
                    { 370, "name", 1, new DateTime(2025, 7, 22, 5, 47, 0, 650, DateTimeKind.Utc).AddTicks(4158), "System", 7, true, null, null, "text" },
                    { 371, "display-name", 1, new DateTime(2025, 7, 22, 5, 47, 0, 650, DateTimeKind.Utc).AddTicks(4158), "System", 7, true, null, null, "text" },
                    { 372, "api-url", 1, new DateTime(2025, 7, 22, 5, 47, 0, 650, DateTimeKind.Utc).AddTicks(4158), "System", 7, true, null, null, "url" },
                    { 373, "dependOn", 1, new DateTime(2025, 7, 22, 5, 47, 0, 650, DateTimeKind.Utc).AddTicks(4158), "System", 7, true, null, null, "text" },
                    { 374, "FloatLable", 2, new DateTime(2025, 7, 22, 5, 47, 0, 650, DateTimeKind.Utc).AddTicks(4158), "System", 7, true, null, null, "text" },
                    { 375, "variant", 2, new DateTime(2025, 7, 22, 5, 47, 0, 650, DateTimeKind.Utc).AddTicks(4158), "System", 7, true, null, null, "options" },
                    { 376, "invalid", 2, new DateTime(2025, 7, 22, 5, 47, 0, 650, DateTimeKind.Utc).AddTicks(4158), "System", 7, true, null, null, "boolean" },
                    { 377, "disabled", 2, new DateTime(2025, 7, 22, 5, 47, 0, 650, DateTimeKind.Utc).AddTicks(4158), "System", 7, true, null, null, "boolean" },
                    { 378, "icon", 2, new DateTime(2025, 7, 22, 5, 47, 0, 650, DateTimeKind.Utc).AddTicks(4158), "System", 7, true, null, null, "icon" },
                    { 379, "placeholder", 2, new DateTime(2025, 7, 22, 5, 47, 0, 650, DateTimeKind.Utc).AddTicks(4158), "System", 7, true, null, null, "text" },
                    { 380, "helpText", 2, new DateTime(2025, 7, 22, 5, 47, 0, 650, DateTimeKind.Utc).AddTicks(4158), "System", 7, true, null, null, "text" },
                    { 381, "required", 3, new DateTime(2025, 7, 22, 5, 47, 0, 650, DateTimeKind.Utc).AddTicks(4158), "System", 7, true, null, null, "boolean" },
                    { 382, "hourFormat", 2, new DateTime(2025, 7, 22, 5, 47, 0, 650, DateTimeKind.Utc).AddTicks(4158), "System", 7, true, null, null, "options" },
                    { 383, "stepHour", 2, new DateTime(2025, 7, 22, 5, 47, 0, 650, DateTimeKind.Utc).AddTicks(4158), "System", 7, true, null, null, "number" },
                    { 384, "stepMinute", 2, new DateTime(2025, 7, 22, 5, 47, 0, 650, DateTimeKind.Utc).AddTicks(4158), "System", 7, true, null, null, "number" },
                    { 385, "stepSecond", 2, new DateTime(2025, 7, 22, 5, 47, 0, 650, DateTimeKind.Utc).AddTicks(4158), "System", 7, true, null, null, "number" },
                    { 386, "showSeconds", 2, new DateTime(2025, 7, 22, 5, 47, 0, 650, DateTimeKind.Utc).AddTicks(4158), "System", 7, true, null, null, "boolean" },
                    { 387, "showIcon", 2, new DateTime(2025, 7, 22, 5, 47, 0, 650, DateTimeKind.Utc).AddTicks(4158), "System", 7, true, null, null, "boolean" },
                    { 388, "iconPos", 2, new DateTime(2025, 7, 22, 5, 47, 0, 650, DateTimeKind.Utc).AddTicks(4158), "System", 7, true, null, null, "options" },
                    { 389, "locale", 2, new DateTime(2025, 7, 22, 5, 47, 0, 650, DateTimeKind.Utc).AddTicks(4158), "System", 7, true, null, null, "text" },
                    { 390, "readOnlyInput", 2, new DateTime(2025, 7, 22, 5, 47, 0, 650, DateTimeKind.Utc).AddTicks(4158), "System", 7, true, null, null, "boolean" },
                    { 391, "autoClear", 2, new DateTime(2025, 7, 22, 5, 47, 0, 650, DateTimeKind.Utc).AddTicks(4158), "System", 7, true, null, null, "boolean" },
                    { 392, "regex", 3, new DateTime(2025, 7, 22, 5, 47, 0, 650, DateTimeKind.Utc).AddTicks(4158), "System", 7, true, null, null, "regex" },

                    // Search field configurations (FieldTypeId = 26)
                    { 393, "name", 1, new DateTime(2025, 7, 22, 5, 47, 0, 650, DateTimeKind.Utc).AddTicks(4158), "System", 26, true, null, null, "text" },
                    { 394, "display-name", 1, new DateTime(2025, 7, 22, 5, 47, 0, 650, DateTimeKind.Utc).AddTicks(4158), "System", 26, true, null, null, "text" },
                    { 395, "api-url", 1, new DateTime(2025, 7, 22, 5, 47, 0, 650, DateTimeKind.Utc).AddTicks(4158), "System", 26, true, null, null, "url" },
                    { 396, "dependOn", 1, new DateTime(2025, 7, 22, 5, 47, 0, 650, DateTimeKind.Utc).AddTicks(4158), "System", 26, true, null, null, "text" },
                    { 397, "FloatLable", 2, new DateTime(2025, 7, 22, 5, 47, 0, 650, DateTimeKind.Utc).AddTicks(4158), "System", 26, true, null, null, "text" },
                    { 398, "variant", 2, new DateTime(2025, 7, 22, 5, 47, 0, 650, DateTimeKind.Utc).AddTicks(4158), "System", 26, true, null, null, "options" },
                    { 399, "invalid", 2, new DateTime(2025, 7, 22, 5, 47, 0, 650, DateTimeKind.Utc).AddTicks(4158), "System", 26, true, null, null, "boolean" },
                    { 400, "disabled", 2, new DateTime(2025, 7, 22, 5, 47, 0, 650, DateTimeKind.Utc).AddTicks(4158), "System", 26, true, null, null, "boolean" },
                    { 401, "icon", 2, new DateTime(2025, 7, 22, 5, 47, 0, 650, DateTimeKind.Utc).AddTicks(4158), "System", 26, true, null, null, "icon" },
                    { 402, "placeholder", 2, new DateTime(2025, 7, 22, 5, 47, 0, 650, DateTimeKind.Utc).AddTicks(4158), "System", 26, true, null, null, "text" },
                    { 403, "helpText", 2, new DateTime(2025, 7, 22, 5, 47, 0, 650, DateTimeKind.Utc).AddTicks(4158), "System", 26, true, null, null, "text" },
                    { 404, "required", 3, new DateTime(2025, 7, 22, 5, 47, 0, 650, DateTimeKind.Utc).AddTicks(4158), "System", 26, true, null, null, "boolean" },
                    { 405, "regex", 3, new DateTime(2025, 7, 22, 5, 47, 0, 650, DateTimeKind.Utc).AddTicks(4158), "System", 26, true, null, null, "regex" },

                    // Select field configurations (FieldTypeId = 27)
                    { 406, "name", 1, new DateTime(2025, 7, 22, 5, 47, 0, 650, DateTimeKind.Utc).AddTicks(4158), "System", 27, true, null, null, "text" },
                    { 407, "display-name", 1, new DateTime(2025, 7, 22, 5, 47, 0, 650, DateTimeKind.Utc).AddTicks(4158), "System", 27, true, null, null, "text" },
                    { 408, "api-url", 1, new DateTime(2025, 7, 22, 5, 47, 0, 650, DateTimeKind.Utc).AddTicks(4158), "System", 27, true, null, null, "url" },
                    { 409, "dependOn", 1, new DateTime(2025, 7, 22, 5, 47, 0, 650, DateTimeKind.Utc).AddTicks(4158), "System", 27, true, null, null, "text" },
                    { 410, "FloatLable", 2, new DateTime(2025, 7, 22, 5, 47, 0, 650, DateTimeKind.Utc).AddTicks(4158), "System", 27, true, null, null, "text" },
                    { 411, "variant", 2, new DateTime(2025, 7, 22, 5, 47, 0, 650, DateTimeKind.Utc).AddTicks(4158), "System", 27, true, null, null, "options" },
                    { 412, "invalid", 2, new DateTime(2025, 7, 22, 5, 47, 0, 650, DateTimeKind.Utc).AddTicks(4158), "System", 27, true, null, null, "boolean" },
                    { 413, "disabled", 2, new DateTime(2025, 7, 22, 5, 47, 0, 650, DateTimeKind.Utc).AddTicks(4158), "System", 27, true, null, null, "boolean" },
                    { 414, "icon", 2, new DateTime(2025, 7, 22, 5, 47, 0, 650, DateTimeKind.Utc).AddTicks(4158), "System", 27, true, null, null, "icon" },
                    { 415, "placeholder", 2, new DateTime(2025, 7, 22, 5, 47, 0, 650, DateTimeKind.Utc).AddTicks(4158), "System", 27, true, null, null, "text" },
                    { 416, "helpText", 2, new DateTime(2025, 7, 22, 5, 47, 0, 650, DateTimeKind.Utc).AddTicks(4158), "System", 27, true, null, null, "text" },
                    { 417, "required", 3, new DateTime(2025, 7, 22, 5, 47, 0, 650, DateTimeKind.Utc).AddTicks(4158), "System", 27, true, null, null, "boolean" },
                    { 418, "options", 2, new DateTime(2025, 7, 22, 5, 47, 0, 650, DateTimeKind.Utc).AddTicks(4158), "System", 27, true, null, null, "array" },
                    { 419, "optionLabel", 2, new DateTime(2025, 7, 22, 5, 47, 0, 650, DateTimeKind.Utc).AddTicks(4158), "System", 27, true, null, null, "text" },
                    { 420, "optionValue", 2, new DateTime(2025, 7, 22, 5, 47, 0, 650, DateTimeKind.Utc).AddTicks(4158), "System", 27, true, null, null, "text" },
                    { 421, "multiple", 2, new DateTime(2025, 7, 22, 5, 47, 0, 650, DateTimeKind.Utc).AddTicks(4158), "System", 27, true, null, null, "boolean" },
                    { 422, "filter", 2, new DateTime(2025, 7, 22, 5, 47, 0, 650, DateTimeKind.Utc).AddTicks(4158), "System", 27, true, null, null, "boolean" },
                    { 423, "showClear", 2, new DateTime(2025, 7, 22, 5, 47, 0, 650, DateTimeKind.Utc).AddTicks(4158), "System", 27, true, null, null, "boolean" },
                    { 424, "regex", 3, new DateTime(2025, 7, 22, 5, 47, 0, 650, DateTimeKind.Utc).AddTicks(4158), "System", 27, true, null, null, "regex" }
                });
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DeleteData(
                table: "config_types",
                keyColumn: "id",
                keyValue: 1);

            migrationBuilder.DeleteData(
                table: "config_types",
                keyColumn: "id",
                keyValue: 2);

            migrationBuilder.DeleteData(
                table: "config_types",
                keyColumn: "id",
                keyValue: 3);

            migrationBuilder.DeleteData(
                table: "field_configs",
                keyColumn: "id",
                keyValue: 1);

            migrationBuilder.DeleteData(
                table: "field_configs",
                keyColumn: "id",
                keyValue: 2);

            migrationBuilder.DeleteData(
                table: "field_configs",
                keyColumn: "id",
                keyValue: 3);

            migrationBuilder.DeleteData(
                table: "field_configs",
                keyColumn: "id",
                keyValue: 4);

            migrationBuilder.DeleteData(
                table: "field_configs",
                keyColumn: "id",
                keyValue: 5);

            migrationBuilder.DeleteData(
                table: "field_configs",
                keyColumn: "id",
                keyValue: 6);

            migrationBuilder.DeleteData(
                table: "field_configs",
                keyColumn: "id",
                keyValue: 7);

            migrationBuilder.DeleteData(
                table: "field_configs",
                keyColumn: "id",
                keyValue: 8);

            migrationBuilder.DeleteData(
                table: "field_configs",
                keyColumn: "id",
                keyValue: 9);

            migrationBuilder.DeleteData(
                table: "field_configs",
                keyColumn: "id",
                keyValue: 10);

            migrationBuilder.DeleteData(
                table: "field_configs",
                keyColumn: "id",
                keyValue: 11);

            migrationBuilder.DeleteData(
                table: "field_configs",
                keyColumn: "id",
                keyValue: 12);

            migrationBuilder.DeleteData(
                table: "field_configs",
                keyColumn: "id",
                keyValue: 13);

            migrationBuilder.DeleteData(
                table: "field_configs",
                keyColumn: "id",
                keyValue: 14);

            migrationBuilder.DeleteData(
                table: "field_configs",
                keyColumn: "id",
                keyValue: 15);

            migrationBuilder.DeleteData(
                table: "field_configs",
                keyColumn: "id",
                keyValue: 16);

            migrationBuilder.DeleteData(
                table: "field_configs",
                keyColumn: "id",
                keyValue: 17);

            migrationBuilder.DeleteData(
                table: "field_configs",
                keyColumn: "id",
                keyValue: 18);

            migrationBuilder.DeleteData(
                table: "field_configs",
                keyColumn: "id",
                keyValue: 19);

            migrationBuilder.DeleteData(
                table: "field_configs",
                keyColumn: "id",
                keyValue: 20);

            migrationBuilder.DeleteData(
                table: "field_configs",
                keyColumn: "id",
                keyValue: 21);

            migrationBuilder.DeleteData(
                table: "field_configs",
                keyColumn: "id",
                keyValue: 22);

            migrationBuilder.DeleteData(
                table: "field_configs",
                keyColumn: "id",
                keyValue: 23);

            migrationBuilder.DeleteData(
                table: "field_configs",
                keyColumn: "id",
                keyValue: 24);

            migrationBuilder.DeleteData(
                table: "field_configs",
                keyColumn: "id",
                keyValue: 25);

            migrationBuilder.DeleteData(
                table: "field_configs",
                keyColumn: "id",
                keyValue: 26);

            migrationBuilder.DeleteData(
                table: "field_configs",
                keyColumn: "id",
                keyValue: 27);

            migrationBuilder.DeleteData(
                table: "field_configs",
                keyColumn: "id",
                keyValue: 28);

            migrationBuilder.DeleteData(
                table: "field_configs",
                keyColumn: "id",
                keyValue: 29);

            migrationBuilder.DeleteData(
                table: "field_configs",
                keyColumn: "id",
                keyValue: 30);

            migrationBuilder.DeleteData(
                table: "field_configs",
                keyColumn: "id",
                keyValue: 31);

            migrationBuilder.DeleteData(
                table: "field_configs",
                keyColumn: "id",
                keyValue: 32);

            migrationBuilder.DeleteData(
                table: "field_configs",
                keyColumn: "id",
                keyValue: 33);

            migrationBuilder.DeleteData(
                table: "field_configs",
                keyColumn: "id",
                keyValue: 34);

            migrationBuilder.DeleteData(
                table: "field_configs",
                keyColumn: "id",
                keyValue: 35);

            migrationBuilder.DeleteData(
                table: "field_configs",
                keyColumn: "id",
                keyValue: 36);

            migrationBuilder.DeleteData(
                table: "field_configs",
                keyColumn: "id",
                keyValue: 37);

            migrationBuilder.DeleteData(
                table: "field_configs",
                keyColumn: "id",
                keyValue: 38);

            migrationBuilder.DeleteData(
                table: "field_configs",
                keyColumn: "id",
                keyValue: 39);

            migrationBuilder.DeleteData(
                table: "field_configs",
                keyColumn: "id",
                keyValue: 40);

            migrationBuilder.DeleteData(
                table: "field_configs",
                keyColumn: "id",
                keyValue: 41);

            migrationBuilder.DeleteData(
                table: "field_configs",
                keyColumn: "id",
                keyValue: 42);

            migrationBuilder.DeleteData(
                table: "field_configs",
                keyColumn: "id",
                keyValue: 43);

            migrationBuilder.DeleteData(
                table: "field_configs",
                keyColumn: "id",
                keyValue: 44);

            migrationBuilder.DeleteData(
                table: "field_configs",
                keyColumn: "id",
                keyValue: 45);

            migrationBuilder.DeleteData(
                table: "field_configs",
                keyColumn: "id",
                keyValue: 46);

            migrationBuilder.DeleteData(
                table: "field_configs",
                keyColumn: "id",
                keyValue: 47);

            migrationBuilder.DeleteData(
                table: "field_configs",
                keyColumn: "id",
                keyValue: 48);

            migrationBuilder.DeleteData(
                table: "field_configs",
                keyColumn: "id",
                keyValue: 49);

            migrationBuilder.DeleteData(
                table: "field_configs",
                keyColumn: "id",
                keyValue: 50);

            migrationBuilder.DeleteData(
                table: "field_configs",
                keyColumn: "id",
                keyValue: 51);

            migrationBuilder.DeleteData(
                table: "field_configs",
                keyColumn: "id",
                keyValue: 52);

            migrationBuilder.DeleteData(
                table: "field_configs",
                keyColumn: "id",
                keyValue: 53);

            migrationBuilder.DeleteData(
                table: "field_configs",
                keyColumn: "id",
                keyValue: 54);

            migrationBuilder.DeleteData(
                table: "field_configs",
                keyColumn: "id",
                keyValue: 55);

            migrationBuilder.DeleteData(
                table: "field_configs",
                keyColumn: "id",
                keyValue: 56);

            migrationBuilder.DeleteData(
                table: "field_configs",
                keyColumn: "id",
                keyValue: 57);

            migrationBuilder.DeleteData(
                table: "field_configs",
                keyColumn: "id",
                keyValue: 58);

            migrationBuilder.DeleteData(
                table: "field_configs",
                keyColumn: "id",
                keyValue: 59);

            migrationBuilder.DeleteData(
                table: "field_configs",
                keyColumn: "id",
                keyValue: 60);

            migrationBuilder.DeleteData(
                table: "field_configs",
                keyColumn: "id",
                keyValue: 61);

            migrationBuilder.DeleteData(
                table: "field_configs",
                keyColumn: "id",
                keyValue: 62);

            migrationBuilder.DeleteData(
                table: "field_configs",
                keyColumn: "id",
                keyValue: 63);

            migrationBuilder.DeleteData(
                table: "field_configs",
                keyColumn: "id",
                keyValue: 64);

            migrationBuilder.DeleteData(
                table: "field_configs",
                keyColumn: "id",
                keyValue: 65);

            migrationBuilder.DeleteData(
                table: "field_configs",
                keyColumn: "id",
                keyValue: 66);

            migrationBuilder.DeleteData(
                table: "field_configs",
                keyColumn: "id",
                keyValue: 67);

            migrationBuilder.DeleteData(
                table: "field_configs",
                keyColumn: "id",
                keyValue: 68);

            migrationBuilder.DeleteData(
                table: "field_configs",
                keyColumn: "id",
                keyValue: 69);

            migrationBuilder.DeleteData(
                table: "field_configs",
                keyColumn: "id",
                keyValue: 70);

            migrationBuilder.DeleteData(
                table: "field_configs",
                keyColumn: "id",
                keyValue: 71);

            migrationBuilder.DeleteData(
                table: "field_configs",
                keyColumn: "id",
                keyValue: 72);

            migrationBuilder.DeleteData(
                table: "field_configs",
                keyColumn: "id",
                keyValue: 73);

            migrationBuilder.DeleteData(
                table: "field_configs",
                keyColumn: "id",
                keyValue: 74);

            migrationBuilder.DeleteData(
                table: "field_configs",
                keyColumn: "id",
                keyValue: 75);

            migrationBuilder.DeleteData(
                table: "field_configs",
                keyColumn: "id",
                keyValue: 76);

            migrationBuilder.DeleteData(
                table: "field_configs",
                keyColumn: "id",
                keyValue: 77);

            migrationBuilder.DeleteData(
                table: "field_configs",
                keyColumn: "id",
                keyValue: 78);

            migrationBuilder.DeleteData(
                table: "field_configs",
                keyColumn: "id",
                keyValue: 79);

            migrationBuilder.DeleteData(
                table: "field_configs",
                keyColumn: "id",
                keyValue: 80);

            migrationBuilder.DeleteData(
                table: "field_configs",
                keyColumn: "id",
                keyValue: 81);

            migrationBuilder.DeleteData(
                table: "field_configs",
                keyColumn: "id",
                keyValue: 82);

            migrationBuilder.DeleteData(
                table: "field_configs",
                keyColumn: "id",
                keyValue: 83);

            migrationBuilder.DeleteData(
                table: "field_configs",
                keyColumn: "id",
                keyValue: 84);

            migrationBuilder.DeleteData(
                table: "field_configs",
                keyColumn: "id",
                keyValue: 85);

            migrationBuilder.DeleteData(
                table: "field_configs",
                keyColumn: "id",
                keyValue: 86);

            migrationBuilder.DeleteData(
                table: "field_configs",
                keyColumn: "id",
                keyValue: 87);

            migrationBuilder.DeleteData(
                table: "field_configs",
                keyColumn: "id",
                keyValue: 88);

            migrationBuilder.DeleteData(
                table: "field_configs",
                keyColumn: "id",
                keyValue: 89);

            migrationBuilder.DeleteData(
                table: "field_configs",
                keyColumn: "id",
                keyValue: 90);

            migrationBuilder.DeleteData(
                table: "field_configs",
                keyColumn: "id",
                keyValue: 91);

            migrationBuilder.DeleteData(
                table: "field_configs",
                keyColumn: "id",
                keyValue: 92);

            migrationBuilder.DeleteData(
                table: "field_configs",
                keyColumn: "id",
                keyValue: 93);

            migrationBuilder.DeleteData(
                table: "field_configs",
                keyColumn: "id",
                keyValue: 94);

            migrationBuilder.DeleteData(
                table: "field_configs",
                keyColumn: "id",
                keyValue: 95);

            migrationBuilder.DeleteData(
                table: "field_configs",
                keyColumn: "id",
                keyValue: 96);

            migrationBuilder.DeleteData(
                table: "field_configs",
                keyColumn: "id",
                keyValue: 97);

            migrationBuilder.DeleteData(
                table: "field_configs",
                keyColumn: "id",
                keyValue: 98);

            migrationBuilder.DeleteData(
                table: "field_configs",
                keyColumn: "id",
                keyValue: 99);

            migrationBuilder.DeleteData(
                table: "field_configs",
                keyColumn: "id",
                keyValue: 100);

            migrationBuilder.DeleteData(
                table: "field_configs",
                keyColumn: "id",
                keyValue: 101);

            migrationBuilder.DeleteData(
                table: "field_configs",
                keyColumn: "id",
                keyValue: 102);

            migrationBuilder.DeleteData(
                table: "field_configs",
                keyColumn: "id",
                keyValue: 103);

            migrationBuilder.DeleteData(
                table: "field_configs",
                keyColumn: "id",
                keyValue: 104);

            migrationBuilder.DeleteData(
                table: "field_configs",
                keyColumn: "id",
                keyValue: 105);

            migrationBuilder.DeleteData(
                table: "field_configs",
                keyColumn: "id",
                keyValue: 106);

            migrationBuilder.DeleteData(
                table: "field_configs",
                keyColumn: "id",
                keyValue: 107);

            migrationBuilder.DeleteData(
                table: "field_configs",
                keyColumn: "id",
                keyValue: 108);

            migrationBuilder.DeleteData(
                table: "field_configs",
                keyColumn: "id",
                keyValue: 109);

            migrationBuilder.DeleteData(
                table: "field_configs",
                keyColumn: "id",
                keyValue: 110);

            migrationBuilder.DeleteData(
                table: "field_configs",
                keyColumn: "id",
                keyValue: 111);

            migrationBuilder.DeleteData(
                table: "field_configs",
                keyColumn: "id",
                keyValue: 112);

            migrationBuilder.DeleteData(
                table: "field_configs",
                keyColumn: "id",
                keyValue: 113);

            migrationBuilder.DeleteData(
                table: "field_configs",
                keyColumn: "id",
                keyValue: 114);

            migrationBuilder.DeleteData(
                table: "field_configs",
                keyColumn: "id",
                keyValue: 115);

            migrationBuilder.DeleteData(
                table: "field_configs",
                keyColumn: "id",
                keyValue: 116);

            migrationBuilder.DeleteData(
                table: "field_configs",
                keyColumn: "id",
                keyValue: 117);

            migrationBuilder.DeleteData(
                table: "field_configs",
                keyColumn: "id",
                keyValue: 118);

            migrationBuilder.DeleteData(
                table: "field_configs",
                keyColumn: "id",
                keyValue: 119);

            migrationBuilder.DeleteData(
                table: "field_configs",
                keyColumn: "id",
                keyValue: 120);

            migrationBuilder.DeleteData(
                table: "field_configs",
                keyColumn: "id",
                keyValue: 121);

            migrationBuilder.DeleteData(
                table: "field_configs",
                keyColumn: "id",
                keyValue: 122);

            migrationBuilder.DeleteData(
                table: "field_configs",
                keyColumn: "id",
                keyValue: 123);

            migrationBuilder.DeleteData(
                table: "field_configs",
                keyColumn: "id",
                keyValue: 124);

            migrationBuilder.DeleteData(
                table: "field_configs",
                keyColumn: "id",
                keyValue: 125);

            migrationBuilder.DeleteData(
                table: "field_configs",
                keyColumn: "id",
                keyValue: 126);

            migrationBuilder.DeleteData(
                table: "field_configs",
                keyColumn: "id",
                keyValue: 127);

            migrationBuilder.DeleteData(
                table: "field_configs",
                keyColumn: "id",
                keyValue: 128);

            migrationBuilder.DeleteData(
                table: "field_configs",
                keyColumn: "id",
                keyValue: 129);

            migrationBuilder.DeleteData(
                table: "field_configs",
                keyColumn: "id",
                keyValue: 130);

            migrationBuilder.DeleteData(
                table: "field_configs",
                keyColumn: "id",
                keyValue: 131);

            migrationBuilder.DeleteData(
                table: "field_configs",
                keyColumn: "id",
                keyValue: 132);

            migrationBuilder.DeleteData(
                table: "field_configs",
                keyColumn: "id",
                keyValue: 133);

            migrationBuilder.DeleteData(
                table: "field_configs",
                keyColumn: "id",
                keyValue: 134);

            migrationBuilder.DeleteData(
                table: "field_configs",
                keyColumn: "id",
                keyValue: 135);

            migrationBuilder.DeleteData(
                table: "field_configs",
                keyColumn: "id",
                keyValue: 136);

            migrationBuilder.DeleteData(
                table: "field_configs",
                keyColumn: "id",
                keyValue: 137);

            migrationBuilder.DeleteData(
                table: "field_configs",
                keyColumn: "id",
                keyValue: 138);

            migrationBuilder.DeleteData(
                table: "field_configs",
                keyColumn: "id",
                keyValue: 139);

            migrationBuilder.DeleteData(
                table: "field_configs",
                keyColumn: "id",
                keyValue: 140);

            migrationBuilder.DeleteData(
                table: "field_configs",
                keyColumn: "id",
                keyValue: 141);

            migrationBuilder.DeleteData(
                table: "field_configs",
                keyColumn: "id",
                keyValue: 142);

            migrationBuilder.DeleteData(
                table: "field_configs",
                keyColumn: "id",
                keyValue: 143);

            migrationBuilder.DeleteData(
                table: "field_configs",
                keyColumn: "id",
                keyValue: 144);

            migrationBuilder.DeleteData(
                table: "field_configs",
                keyColumn: "id",
                keyValue: 145);

            migrationBuilder.DeleteData(
                table: "field_configs",
                keyColumn: "id",
                keyValue: 146);

            migrationBuilder.DeleteData(
                table: "field_configs",
                keyColumn: "id",
                keyValue: 147);

            migrationBuilder.DeleteData(
                table: "field_configs",
                keyColumn: "id",
                keyValue: 148);

            migrationBuilder.DeleteData(
                table: "field_configs",
                keyColumn: "id",
                keyValue: 149);

            migrationBuilder.DeleteData(
                table: "field_configs",
                keyColumn: "id",
                keyValue: 150);

            migrationBuilder.DeleteData(
                table: "field_configs",
                keyColumn: "id",
                keyValue: 151);

            migrationBuilder.DeleteData(
                table: "field_configs",
                keyColumn: "id",
                keyValue: 152);

            migrationBuilder.DeleteData(
                table: "field_configs",
                keyColumn: "id",
                keyValue: 153);

            migrationBuilder.DeleteData(
                table: "field_configs",
                keyColumn: "id",
                keyValue: 154);

            migrationBuilder.DeleteData(
                table: "field_configs",
                keyColumn: "id",
                keyValue: 155);

            migrationBuilder.DeleteData(
                table: "field_configs",
                keyColumn: "id",
                keyValue: 156);

            migrationBuilder.DeleteData(
                table: "field_configs",
                keyColumn: "id",
                keyValue: 157);

            migrationBuilder.DeleteData(
                table: "field_configs",
                keyColumn: "id",
                keyValue: 158);

            migrationBuilder.DeleteData(
                table: "field_configs",
                keyColumn: "id",
                keyValue: 159);

            migrationBuilder.DeleteData(
                table: "field_configs",
                keyColumn: "id",
                keyValue: 160);

            migrationBuilder.DeleteData(
                table: "field_configs",
                keyColumn: "id",
                keyValue: 161);

            migrationBuilder.DeleteData(
                table: "field_configs",
                keyColumn: "id",
                keyValue: 162);

            migrationBuilder.DeleteData(
                table: "field_configs",
                keyColumn: "id",
                keyValue: 163);

            migrationBuilder.DeleteData(
                table: "field_configs",
                keyColumn: "id",
                keyValue: 164);

            migrationBuilder.DeleteData(
                table: "field_configs",
                keyColumn: "id",
                keyValue: 165);

            migrationBuilder.DeleteData(
                table: "field_configs",
                keyColumn: "id",
                keyValue: 166);

            migrationBuilder.DeleteData(
                table: "field_configs",
                keyColumn: "id",
                keyValue: 167);

            migrationBuilder.DeleteData(
                table: "field_configs",
                keyColumn: "id",
                keyValue: 168);

            migrationBuilder.DeleteData(
                table: "field_configs",
                keyColumn: "id",
                keyValue: 169);

            migrationBuilder.DeleteData(
                table: "field_configs",
                keyColumn: "id",
                keyValue: 170);

            migrationBuilder.DeleteData(
                table: "field_configs",
                keyColumn: "id",
                keyValue: 171);

            migrationBuilder.DeleteData(
                table: "field_configs",
                keyColumn: "id",
                keyValue: 172);

            migrationBuilder.DeleteData(
                table: "field_configs",
                keyColumn: "id",
                keyValue: 173);

            migrationBuilder.DeleteData(
                table: "field_configs",
                keyColumn: "id",
                keyValue: 174);

            migrationBuilder.DeleteData(
                table: "field_configs",
                keyColumn: "id",
                keyValue: 175);

            migrationBuilder.DeleteData(
                table: "field_configs",
                keyColumn: "id",
                keyValue: 176);

            migrationBuilder.DeleteData(
                table: "field_configs",
                keyColumn: "id",
                keyValue: 177);

            migrationBuilder.DeleteData(
                table: "field_configs",
                keyColumn: "id",
                keyValue: 178);

            migrationBuilder.DeleteData(
                table: "field_configs",
                keyColumn: "id",
                keyValue: 179);

            migrationBuilder.DeleteData(
                table: "field_configs",
                keyColumn: "id",
                keyValue: 180);

            migrationBuilder.DeleteData(
                table: "field_configs",
                keyColumn: "id",
                keyValue: 181);

            migrationBuilder.DeleteData(
                table: "field_configs",
                keyColumn: "id",
                keyValue: 182);

            migrationBuilder.DeleteData(
                table: "field_configs",
                keyColumn: "id",
                keyValue: 183);

            migrationBuilder.DeleteData(
                table: "field_configs",
                keyColumn: "id",
                keyValue: 184);

            migrationBuilder.DeleteData(
                table: "field_configs",
                keyColumn: "id",
                keyValue: 185);

            migrationBuilder.DeleteData(
                table: "field_configs",
                keyColumn: "id",
                keyValue: 186);

            migrationBuilder.DeleteData(
                table: "field_configs",
                keyColumn: "id",
                keyValue: 187);

            migrationBuilder.DeleteData(
                table: "field_configs",
                keyColumn: "id",
                keyValue: 188);

            migrationBuilder.DeleteData(
                table: "field_configs",
                keyColumn: "id",
                keyValue: 189);

            migrationBuilder.DeleteData(
                table: "field_configs",
                keyColumn: "id",
                keyValue: 190);

            migrationBuilder.DeleteData(
                table: "field_configs",
                keyColumn: "id",
                keyValue: 191);

            migrationBuilder.DeleteData(
                table: "field_configs",
                keyColumn: "id",
                keyValue: 192);

            migrationBuilder.DeleteData(
                table: "field_configs",
                keyColumn: "id",
                keyValue: 193);

            migrationBuilder.DeleteData(
                table: "field_configs",
                keyColumn: "id",
                keyValue: 194);

            migrationBuilder.DeleteData(
                table: "field_configs",
                keyColumn: "id",
                keyValue: 195);

            migrationBuilder.DeleteData(
                table: "field_configs",
                keyColumn: "id",
                keyValue: 196);

            migrationBuilder.DeleteData(
                table: "field_configs",
                keyColumn: "id",
                keyValue: 197);

            migrationBuilder.DeleteData(
                table: "field_configs",
                keyColumn: "id",
                keyValue: 198);

            migrationBuilder.DeleteData(
                table: "field_configs",
                keyColumn: "id",
                keyValue: 199);

            migrationBuilder.DeleteData(
                table: "field_configs",
                keyColumn: "id",
                keyValue: 200);

            migrationBuilder.DeleteData(
                table: "field_configs",
                keyColumn: "id",
                keyValue: 201);

            migrationBuilder.DeleteData(
                table: "field_configs",
                keyColumn: "id",
                keyValue: 202);

            migrationBuilder.DeleteData(
                table: "field_configs",
                keyColumn: "id",
                keyValue: 203);

            migrationBuilder.DeleteData(
                table: "field_configs",
                keyColumn: "id",
                keyValue: 204);

            migrationBuilder.DeleteData(
                table: "field_configs",
                keyColumn: "id",
                keyValue: 205);

            migrationBuilder.DeleteData(
                table: "field_configs",
                keyColumn: "id",
                keyValue: 206);

            migrationBuilder.DeleteData(
                table: "field_configs",
                keyColumn: "id",
                keyValue: 207);

            migrationBuilder.DeleteData(
                table: "field_configs",
                keyColumn: "id",
                keyValue: 208);

            migrationBuilder.DeleteData(
                table: "field_configs",
                keyColumn: "id",
                keyValue: 209);

            migrationBuilder.DeleteData(
                table: "field_configs",
                keyColumn: "id",
                keyValue: 210);

            migrationBuilder.DeleteData(
                table: "field_configs",
                keyColumn: "id",
                keyValue: 211);

            migrationBuilder.DeleteData(
                table: "field_configs",
                keyColumn: "id",
                keyValue: 212);

            migrationBuilder.DeleteData(
                table: "field_configs",
                keyColumn: "id",
                keyValue: 213);

            migrationBuilder.DeleteData(
                table: "field_configs",
                keyColumn: "id",
                keyValue: 214);

            migrationBuilder.DeleteData(
                table: "field_configs",
                keyColumn: "id",
                keyValue: 215);

            migrationBuilder.DeleteData(
                table: "field_configs",
                keyColumn: "id",
                keyValue: 216);

            migrationBuilder.DeleteData(
                table: "field_configs",
                keyColumn: "id",
                keyValue: 217);

            migrationBuilder.DeleteData(
                table: "field_configs",
                keyColumn: "id",
                keyValue: 218);

            migrationBuilder.DeleteData(
                table: "field_configs",
                keyColumn: "id",
                keyValue: 219);

            migrationBuilder.DeleteData(
                table: "field_configs",
                keyColumn: "id",
                keyValue: 220);

            migrationBuilder.DeleteData(
                table: "field_configs",
                keyColumn: "id",
                keyValue: 221);

            migrationBuilder.DeleteData(
                table: "field_configs",
                keyColumn: "id",
                keyValue: 222);

            migrationBuilder.DeleteData(
                table: "field_configs",
                keyColumn: "id",
                keyValue: 223);

            migrationBuilder.DeleteData(
                table: "field_configs",
                keyColumn: "id",
                keyValue: 224);

            migrationBuilder.DeleteData(
                table: "field_configs",
                keyColumn: "id",
                keyValue: 225);

            migrationBuilder.DeleteData(
                table: "field_configs",
                keyColumn: "id",
                keyValue: 226);

            migrationBuilder.DeleteData(
                table: "field_configs",
                keyColumn: "id",
                keyValue: 227);

            migrationBuilder.DeleteData(
                table: "field_configs",
                keyColumn: "id",
                keyValue: 228);

            migrationBuilder.DeleteData(
                table: "field_configs",
                keyColumn: "id",
                keyValue: 229);

            migrationBuilder.DeleteData(
                table: "field_configs",
                keyColumn: "id",
                keyValue: 230);

            migrationBuilder.DeleteData(
                table: "field_configs",
                keyColumn: "id",
                keyValue: 231);

            migrationBuilder.DeleteData(
                table: "field_configs",
                keyColumn: "id",
                keyValue: 232);

            migrationBuilder.DeleteData(
                table: "field_configs",
                keyColumn: "id",
                keyValue: 233);

            migrationBuilder.DeleteData(
                table: "field_configs",
                keyColumn: "id",
                keyValue: 234);

            migrationBuilder.DeleteData(
                table: "field_configs",
                keyColumn: "id",
                keyValue: 235);

            migrationBuilder.DeleteData(
                table: "field_configs",
                keyColumn: "id",
                keyValue: 236);

            migrationBuilder.DeleteData(
                table: "field_configs",
                keyColumn: "id",
                keyValue: 237);

            migrationBuilder.DeleteData(
                table: "field_configs",
                keyColumn: "id",
                keyValue: 238);

            migrationBuilder.DeleteData(
                table: "field_configs",
                keyColumn: "id",
                keyValue: 239);

            migrationBuilder.DeleteData(
                table: "field_configs",
                keyColumn: "id",
                keyValue: 240);

            migrationBuilder.DeleteData(
                table: "field_configs",
                keyColumn: "id",
                keyValue: 241);

            migrationBuilder.DeleteData(
                table: "field_configs",
                keyColumn: "id",
                keyValue: 242);

            migrationBuilder.DeleteData(
                table: "field_configs",
                keyColumn: "id",
                keyValue: 243);

            migrationBuilder.DeleteData(
                table: "field_configs",
                keyColumn: "id",
                keyValue: 244);

            migrationBuilder.DeleteData(
                table: "field_configs",
                keyColumn: "id",
                keyValue: 245);

            migrationBuilder.DeleteData(
                table: "field_configs",
                keyColumn: "id",
                keyValue: 246);

            migrationBuilder.DeleteData(
                table: "field_configs",
                keyColumn: "id",
                keyValue: 247);

            migrationBuilder.DeleteData(
                table: "field_configs",
                keyColumn: "id",
                keyValue: 248);

            migrationBuilder.DeleteData(
                table: "field_configs",
                keyColumn: "id",
                keyValue: 249);

            migrationBuilder.DeleteData(
                table: "field_configs",
                keyColumn: "id",
                keyValue: 250);

            // Adding deletions for field configs 251-300
            migrationBuilder.DeleteData(table: "field_configs", keyColumn: "id", keyValue: 251);
            migrationBuilder.DeleteData(table: "field_configs", keyColumn: "id", keyValue: 252);
            migrationBuilder.DeleteData(table: "field_configs", keyColumn: "id", keyValue: 253);
            migrationBuilder.DeleteData(table: "field_configs", keyColumn: "id", keyValue: 254);
            migrationBuilder.DeleteData(table: "field_configs", keyColumn: "id", keyValue: 255);
            migrationBuilder.DeleteData(table: "field_configs", keyColumn: "id", keyValue: 256);
            migrationBuilder.DeleteData(table: "field_configs", keyColumn: "id", keyValue: 257);
            migrationBuilder.DeleteData(table: "field_configs", keyColumn: "id", keyValue: 258);
            migrationBuilder.DeleteData(table: "field_configs", keyColumn: "id", keyValue: 259);
            migrationBuilder.DeleteData(table: "field_configs", keyColumn: "id", keyValue: 260);
            migrationBuilder.DeleteData(table: "field_configs", keyColumn: "id", keyValue: 261);
            migrationBuilder.DeleteData(table: "field_configs", keyColumn: "id", keyValue: 262);
            migrationBuilder.DeleteData(table: "field_configs", keyColumn: "id", keyValue: 263);
            migrationBuilder.DeleteData(table: "field_configs", keyColumn: "id", keyValue: 264);
            migrationBuilder.DeleteData(table: "field_configs", keyColumn: "id", keyValue: 265);
            migrationBuilder.DeleteData(table: "field_configs", keyColumn: "id", keyValue: 266);
            migrationBuilder.DeleteData(table: "field_configs", keyColumn: "id", keyValue: 267);
            migrationBuilder.DeleteData(table: "field_configs", keyColumn: "id", keyValue: 268);
            migrationBuilder.DeleteData(table: "field_configs", keyColumn: "id", keyValue: 269);
            migrationBuilder.DeleteData(table: "field_configs", keyColumn: "id", keyValue: 270);
            migrationBuilder.DeleteData(table: "field_configs", keyColumn: "id", keyValue: 271);
            migrationBuilder.DeleteData(table: "field_configs", keyColumn: "id", keyValue: 272);
            migrationBuilder.DeleteData(table: "field_configs", keyColumn: "id", keyValue: 273);
            migrationBuilder.DeleteData(table: "field_configs", keyColumn: "id", keyValue: 274);
            migrationBuilder.DeleteData(table: "field_configs", keyColumn: "id", keyValue: 275);
            migrationBuilder.DeleteData(table: "field_configs", keyColumn: "id", keyValue: 276);
            migrationBuilder.DeleteData(table: "field_configs", keyColumn: "id", keyValue: 277);
            migrationBuilder.DeleteData(table: "field_configs", keyColumn: "id", keyValue: 278);
            migrationBuilder.DeleteData(table: "field_configs", keyColumn: "id", keyValue: 279);
            migrationBuilder.DeleteData(table: "field_configs", keyColumn: "id", keyValue: 280);
            migrationBuilder.DeleteData(table: "field_configs", keyColumn: "id", keyValue: 281);
            migrationBuilder.DeleteData(table: "field_configs", keyColumn: "id", keyValue: 282);
            migrationBuilder.DeleteData(table: "field_configs", keyColumn: "id", keyValue: 283);
            migrationBuilder.DeleteData(table: "field_configs", keyColumn: "id", keyValue: 284);
            migrationBuilder.DeleteData(table: "field_configs", keyColumn: "id", keyValue: 285);
            migrationBuilder.DeleteData(table: "field_configs", keyColumn: "id", keyValue: 286);
            migrationBuilder.DeleteData(table: "field_configs", keyColumn: "id", keyValue: 287);
            migrationBuilder.DeleteData(table: "field_configs", keyColumn: "id", keyValue: 288);
            migrationBuilder.DeleteData(table: "field_configs", keyColumn: "id", keyValue: 289);
            migrationBuilder.DeleteData(table: "field_configs", keyColumn: "id", keyValue: 290);
            migrationBuilder.DeleteData(table: "field_configs", keyColumn: "id", keyValue: 291);
            migrationBuilder.DeleteData(table: "field_configs", keyColumn: "id", keyValue: 292);
            migrationBuilder.DeleteData(table: "field_configs", keyColumn: "id", keyValue: 293);
            migrationBuilder.DeleteData(table: "field_configs", keyColumn: "id", keyValue: 294);
            migrationBuilder.DeleteData(table: "field_configs", keyColumn: "id", keyValue: 295);
            migrationBuilder.DeleteData(table: "field_configs", keyColumn: "id", keyValue: 296);
            migrationBuilder.DeleteData(table: "field_configs", keyColumn: "id", keyValue: 297);
            migrationBuilder.DeleteData(table: "field_configs", keyColumn: "id", keyValue: 298);
            migrationBuilder.DeleteData(table: "field_configs", keyColumn: "id", keyValue: 299);
            migrationBuilder.DeleteData(table: "field_configs", keyColumn: "id", keyValue: 300);

            // Adding deletions for field configs 301-350
            migrationBuilder.DeleteData(table: "field_configs", keyColumn: "id", keyValue: 301);
            migrationBuilder.DeleteData(table: "field_configs", keyColumn: "id", keyValue: 302);
            migrationBuilder.DeleteData(table: "field_configs", keyColumn: "id", keyValue: 303);
            migrationBuilder.DeleteData(table: "field_configs", keyColumn: "id", keyValue: 304);
            migrationBuilder.DeleteData(table: "field_configs", keyColumn: "id", keyValue: 305);
            migrationBuilder.DeleteData(table: "field_configs", keyColumn: "id", keyValue: 306);
            migrationBuilder.DeleteData(table: "field_configs", keyColumn: "id", keyValue: 307);
            migrationBuilder.DeleteData(table: "field_configs", keyColumn: "id", keyValue: 308);
            migrationBuilder.DeleteData(table: "field_configs", keyColumn: "id", keyValue: 309);
            migrationBuilder.DeleteData(table: "field_configs", keyColumn: "id", keyValue: 310);
            migrationBuilder.DeleteData(table: "field_configs", keyColumn: "id", keyValue: 311);
            migrationBuilder.DeleteData(table: "field_configs", keyColumn: "id", keyValue: 312);
            migrationBuilder.DeleteData(table: "field_configs", keyColumn: "id", keyValue: 313);
            migrationBuilder.DeleteData(table: "field_configs", keyColumn: "id", keyValue: 315);
            migrationBuilder.DeleteData(table: "field_configs", keyColumn: "id", keyValue: 316);
            migrationBuilder.DeleteData(table: "field_configs", keyColumn: "id", keyValue: 317);
            migrationBuilder.DeleteData(table: "field_configs", keyColumn: "id", keyValue: 318);
            migrationBuilder.DeleteData(table: "field_configs", keyColumn: "id", keyValue: 319);
            migrationBuilder.DeleteData(table: "field_configs", keyColumn: "id", keyValue: 320);
            migrationBuilder.DeleteData(table: "field_configs", keyColumn: "id", keyValue: 321);
            migrationBuilder.DeleteData(table: "field_configs", keyColumn: "id", keyValue: 322);
            migrationBuilder.DeleteData(table: "field_configs", keyColumn: "id", keyValue: 323);
            migrationBuilder.DeleteData(table: "field_configs", keyColumn: "id", keyValue: 324);
            migrationBuilder.DeleteData(table: "field_configs", keyColumn: "id", keyValue: 325);
            migrationBuilder.DeleteData(table: "field_configs", keyColumn: "id", keyValue: 326);
            migrationBuilder.DeleteData(table: "field_configs", keyColumn: "id", keyValue: 327);
            migrationBuilder.DeleteData(table: "field_configs", keyColumn: "id", keyValue: 328);
            migrationBuilder.DeleteData(table: "field_configs", keyColumn: "id", keyValue: 329);
            migrationBuilder.DeleteData(table: "field_configs", keyColumn: "id", keyValue: 330);
            migrationBuilder.DeleteData(table: "field_configs", keyColumn: "id", keyValue: 331);
            migrationBuilder.DeleteData(table: "field_configs", keyColumn: "id", keyValue: 332);
            migrationBuilder.DeleteData(table: "field_configs", keyColumn: "id", keyValue: 333);
            migrationBuilder.DeleteData(table: "field_configs", keyColumn: "id", keyValue: 334);
            migrationBuilder.DeleteData(table: "field_configs", keyColumn: "id", keyValue: 335);
            migrationBuilder.DeleteData(table: "field_configs", keyColumn: "id", keyValue: 336);
            migrationBuilder.DeleteData(table: "field_configs", keyColumn: "id", keyValue: 337);
            migrationBuilder.DeleteData(table: "field_configs", keyColumn: "id", keyValue: 338);
            migrationBuilder.DeleteData(table: "field_configs", keyColumn: "id", keyValue: 339);
            migrationBuilder.DeleteData(table: "field_configs", keyColumn: "id", keyValue: 340);
            migrationBuilder.DeleteData(table: "field_configs", keyColumn: "id", keyValue: 341);
            migrationBuilder.DeleteData(table: "field_configs", keyColumn: "id", keyValue: 342);
            migrationBuilder.DeleteData(table: "field_configs", keyColumn: "id", keyValue: 343);
            migrationBuilder.DeleteData(table: "field_configs", keyColumn: "id", keyValue: 344);
            migrationBuilder.DeleteData(table: "field_configs", keyColumn: "id", keyValue: 345);
            migrationBuilder.DeleteData(table: "field_configs", keyColumn: "id", keyValue: 346);
            migrationBuilder.DeleteData(table: "field_configs", keyColumn: "id", keyValue: 347);
            migrationBuilder.DeleteData(table: "field_configs", keyColumn: "id", keyValue: 348);
            migrationBuilder.DeleteData(table: "field_configs", keyColumn: "id", keyValue: 349);
            migrationBuilder.DeleteData(table: "field_configs", keyColumn: "id", keyValue: 350);

            // Adding deletions for field configs 351-369
            migrationBuilder.DeleteData(table: "field_configs", keyColumn: "id", keyValue: 351);
            migrationBuilder.DeleteData(table: "field_configs", keyColumn: "id", keyValue: 352);
            migrationBuilder.DeleteData(table: "field_configs", keyColumn: "id", keyValue: 353);
            migrationBuilder.DeleteData(table: "field_configs", keyColumn: "id", keyValue: 354);
            migrationBuilder.DeleteData(table: "field_configs", keyColumn: "id", keyValue: 355);
            migrationBuilder.DeleteData(table: "field_configs", keyColumn: "id", keyValue: 356);
            migrationBuilder.DeleteData(table: "field_configs", keyColumn: "id", keyValue: 357);
            migrationBuilder.DeleteData(table: "field_configs", keyColumn: "id", keyValue: 358);
            migrationBuilder.DeleteData(table: "field_configs", keyColumn: "id", keyValue: 359);
            migrationBuilder.DeleteData(table: "field_configs", keyColumn: "id", keyValue: 360);
            migrationBuilder.DeleteData(table: "field_configs", keyColumn: "id", keyValue: 361);
            migrationBuilder.DeleteData(table: "field_configs", keyColumn: "id", keyValue: 362);
            migrationBuilder.DeleteData(table: "field_configs", keyColumn: "id", keyValue: 363);
            migrationBuilder.DeleteData(table: "field_configs", keyColumn: "id", keyValue: 364);
            migrationBuilder.DeleteData(table: "field_configs", keyColumn: "id", keyValue: 365);
            migrationBuilder.DeleteData(table: "field_configs", keyColumn: "id", keyValue: 366);
            migrationBuilder.DeleteData(table: "field_configs", keyColumn: "id", keyValue: 367);
            migrationBuilder.DeleteData(table: "field_configs", keyColumn: "id", keyValue: 368);
            migrationBuilder.DeleteData(table: "field_configs", keyColumn: "id", keyValue: 369);

            migrationBuilder.DeleteData(
                table: "field_types",
                keyColumn: "id",
                keyValue: 2);

            migrationBuilder.DeleteData(
                table: "field_types",
                keyColumn: "id",
                keyValue: 3);

            migrationBuilder.DeleteData(
                table: "field_types",
                keyColumn: "id",
                keyValue: 4);

            migrationBuilder.DeleteData(
                table: "field_types",
                keyColumn: "id",
                keyValue: 5);

            migrationBuilder.DeleteData(
                table: "field_types",
                keyColumn: "id",
                keyValue: 6);

            migrationBuilder.DeleteData(
                table: "field_types",
                keyColumn: "id",
                keyValue: 7);

            migrationBuilder.DeleteData(
                table: "field_types",
                keyColumn: "id",
                keyValue: 8);

            migrationBuilder.DeleteData(
                table: "field_types",
                keyColumn: "id",
                keyValue: 9);

            migrationBuilder.DeleteData(
                table: "field_types",
                keyColumn: "id",
                keyValue: 10);

            migrationBuilder.DeleteData(
                table: "field_types",
                keyColumn: "id",
                keyValue: 11);

            migrationBuilder.DeleteData(
                table: "field_types",
                keyColumn: "id",
                keyValue: 12);

            migrationBuilder.DeleteData(
                table: "field_types",
                keyColumn: "id",
                keyValue: 13);

            migrationBuilder.DeleteData(
                table: "field_types",
                keyColumn: "id",
                keyValue: 14);

            migrationBuilder.DeleteData(
                table: "field_types",
                keyColumn: "id",
                keyValue: 15);

            migrationBuilder.DeleteData(
                table: "field_types",
                keyColumn: "id",
                keyValue: 16);

            migrationBuilder.DeleteData(
                table: "field_types",
                keyColumn: "id",
                keyValue: 17);

            migrationBuilder.DeleteData(
                table: "field_types",
                keyColumn: "id",
                keyValue: 18);

            migrationBuilder.DeleteData(
                table: "field_types",
                keyColumn: "id",
                keyValue: 19);

            migrationBuilder.DeleteData(
                table: "field_types",
                keyColumn: "id",
                keyValue: 20);

            migrationBuilder.DeleteData(
                table: "field_types",
                keyColumn: "id",
                keyValue: 21);

            migrationBuilder.DeleteData(
                table: "field_types",
                keyColumn: "id",
                keyValue: 22);

            migrationBuilder.DeleteData(
                table: "field_types",
                keyColumn: "id",
                keyValue: 23);

            migrationBuilder.DeleteData(
                table: "field_types",
                keyColumn: "id",
                keyValue: 24);

            migrationBuilder.DeleteData(
                table: "field_types",
                keyColumn: "id",
                keyValue: 25);

            migrationBuilder.DeleteData(
                table: "config_types",
                keyColumn: "id",
                keyValue: 1);

            migrationBuilder.DeleteData(
                table: "config_types",
                keyColumn: "id",
                keyValue: 2);

            migrationBuilder.DeleteData(
                table: "config_types",
                keyColumn: "id",
                keyValue: 3);

            migrationBuilder.DeleteData(
                table: "field_types",
                keyColumn: "id",
                keyValue: 1);
        }
    }
}
